import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import { Button } from "@/components/Button";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import FormItem from "@/components/ui/FormItem";
import showToast from "@/utils/toast";

const rexorApiSettingsSchema = z.object({
  apiBaseUrl: z
    .string()
    .optional()
    .refine((val) => !val || z.string().url().safeParse(val).success, {
      message: "Must be a valid URL",
    }),
  authUrl: z
    .string()
    .optional()
    .refine((val) => !val || z.string().url().safeParse(val).success, {
      message: "Must be a valid URL",
    }),
  clientIdDev: z.string().optional(),
  clientIdProd: z.string().optional(),
  apiHost: z.string().optional(),
});

const RexorApiSettings = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm({
    resolver: zodResolver(rexorApiSettingsSchema),
    defaultValues: {
      apiBaseUrl: "",
      authUrl: "",
      clientIdDev: "",
      clientIdProd: "",
      apiHost: "",
    },
  });

  // Fetch current settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `${API_BASE}/admin/system/rexor-api-settings`,
          {
            method: "GET",
            headers: baseHeaders(),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch Rexor API settings");
        }

        const data = await response.json();
        const settings = data.settings;

        // Set form values with current settings or empty strings for defaults
        setValue(
          "apiBaseUrl",
          settings.apiBaseUrl === "https://api.rexor.se/v231/Api"
            ? ""
            : settings.apiBaseUrl
        );
        setValue(
          "authUrl",
          settings.authUrl === "https://auth.rexor.se/v231/Token"
            ? ""
            : settings.authUrl
        );
        setValue(
          "clientIdDev",
          settings.clientIdDev === "testfoyen" ? "" : settings.clientIdDev
        );
        setValue(
          "clientIdProd",
          settings.clientIdProd === "foyen" ? "" : settings.clientIdProd
        );
        setValue(
          "apiHost",
          settings.apiHost === "api.rexor.se" ? "" : settings.apiHost
        );
      } catch (error) {
        console.error("Error fetching Rexor API settings:", error);
        showToast(t("rexor.api-settings.error-message"), "error");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [setValue, t]);

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(
        `${API_BASE}/admin/system/rexor-api-settings`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to save Rexor API settings");
      }

      showToast(t("rexor.api-settings.success-message"), "success");
    } catch (error) {
      console.error("Error saving Rexor API settings:", error);
      showToast(t("rexor.api-settings.error-message"), "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    reset({
      apiBaseUrl: "",
      authUrl: "",
      clientIdDev: "",
      clientIdProd: "",
      apiHost: "",
    });
  };

  if (isLoading) {
    return (
      <div className="mt-4 p-4 border-border rounded-lg bg-elevated shadow">
        <p className="text-sm text-foreground text-opacity-60">
          Loading Rexor API settings...
        </p>
      </div>
    );
  }

  return (
    <div className="mt-4 p-4 border-border rounded-lg bg-elevated shadow">
      <h3 className="text-lg font-semibold mb-2 text-foreground">
        {t("rexor.api-settings.title")}
      </h3>
      <p className="text-sm text-foreground text-opacity-60 mb-4">
        {t("rexor.api-settings.description")}
      </p>

      <div className="space-y-4">
        <FormItem>
          <Label htmlFor="apiBaseUrl">
            {t("rexor.api-settings.api-base-url")}
          </Label>
          <Input
            id="apiBaseUrl"
            {...register("apiBaseUrl")}
            placeholder={t("rexor.api-settings.api-base-url-placeholder")}
            className="w-full"
          />
          {errors.apiBaseUrl && (
            <p className="text-red-600 text-sm mt-1">
              {errors.apiBaseUrl.message}
            </p>
          )}
        </FormItem>

        <FormItem>
          <Label htmlFor="authUrl">{t("rexor.api-settings.auth-url")}</Label>
          <Input
            id="authUrl"
            {...register("authUrl")}
            placeholder={t("rexor.api-settings.auth-url-placeholder")}
            className="w-full"
          />
          {errors.authUrl && (
            <p className="text-red-600 text-sm mt-1">
              {errors.authUrl.message}
            </p>
          )}
        </FormItem>

        <FormItem>
          <Label htmlFor="clientIdDev">
            {t("rexor.api-settings.client-id-dev")}
          </Label>
          <Input
            id="clientIdDev"
            {...register("clientIdDev")}
            placeholder={t("rexor.api-settings.client-id-dev-placeholder")}
            className="w-full"
          />
          {errors.clientIdDev && (
            <p className="text-red-600 text-sm mt-1">
              {errors.clientIdDev.message}
            </p>
          )}
        </FormItem>

        <FormItem>
          <Label htmlFor="clientIdProd">
            {t("rexor.api-settings.client-id-prod")}
          </Label>
          <Input
            id="clientIdProd"
            {...register("clientIdProd")}
            placeholder={t("rexor.api-settings.client-id-prod-placeholder")}
            className="w-full"
          />
          {errors.clientIdProd && (
            <p className="text-red-600 text-sm mt-1">
              {errors.clientIdProd.message}
            </p>
          )}
        </FormItem>

        <FormItem>
          <Label htmlFor="apiHost">{t("rexor.api-settings.api-host")}</Label>
          <Input
            id="apiHost"
            {...register("apiHost")}
            placeholder={t("rexor.api-settings.api-host-placeholder")}
            className="w-full"
          />
          {errors.apiHost && (
            <p className="text-red-600 text-sm mt-1">
              {errors.apiHost.message}
            </p>
          )}
        </FormItem>

        <div className="flex gap-2 pt-2">
          <Button
            type="button"
            onClick={handleSubmit(onSubmit)}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : t("rexor.api-settings.save-button")}
          </Button>
          <Button type="button" variant="outline" onClick={handleReset}>
            {t("rexor.api-settings.reset-button")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RexorApiSettings;
