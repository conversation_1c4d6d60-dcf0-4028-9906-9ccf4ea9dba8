export default {
  cdbProgress: {
    "close-msg": "Är du säker på att du vill avbryta processen?",
    general: {
      placeholderSubTask: "Bearbetar objekt {{index}}...",
    },

    main: {
      step1: {
        label: "Generera lista över sektioner",
        desc: "Använder huvuddokumentet för att skapa en initial struktur.",
      },
      step2: {
        label: "Bearbeta dokument",
        desc: "Genererar beskrivningar och kontrollerar relevans.",
      },
      step3: {
        label: "Koppla dokument till sektioner",
        desc: "Tilldelar relevanta dokument till varje sektion.",
      },
      step4: {
        label: "Identifiera juridiska frågor",
        desc: "Extraherar centrala juridiska frågor för varje sektion.",
      },
      step5: {
        label: "Generera juridiska PM",
        desc: "Skapar juridiska promemorior för de identifierade frågorna.",
      },
      step6: {
        label: "Utforma sektioner",
        desc: "Komponerar innehållet för varje enskild sektion.",
      },
      step7: {
        label: "Kombinera & slutför dokument",
        desc: "Sammanställer sektionerna till det slutliga dokumentet.",
      },
    },

    noMain: {
      step1: {
        label: "Bearbetar dokument",
        desc: "Genererar beskrivningar för alla uppladdade filer.",
      },
      step2: {
        label: "Generera lista över sektioner",
        desc: "Skapar en strukturerad lista över sektioner från dokumentsammanfattningar.",
      },
      step3: {
        label: "Slutför dokumentkoppling",
        desc: "Bekräftar dokumentens relevans för varje planerad sektion.",
      },
      step4: {
        label: "Identifiera juridiska frågor",
        desc: "Extraherar centrala juridiska frågor för varje sektion.",
      },
      step5: {
        label: "Generera juridiska PM",
        desc: "Skapar juridiska promemorior för de identifierade frågorna.",
      },
      step6: {
        label: "Utforma sektioner",
        desc: "Komponerar innehållet för varje enskild sektion.",
      },
      step7: {
        label: "Kombinera & slutför dokument",
        desc: "Sammanställer alla sektioner till det slutliga juridiska dokumentet.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Bearbetar referensfiler",
        desc: "Bearbetar referensfiler",
      },
      step2: {
        label: "Bearbetar granskningsfiler",
        desc: "Bearbetar granskningsfiler",
      },
      step3: {
        label: "Genererar sektionslista",
        desc: "Genererar sektionslista",
      },
      step4: {
        label: "Utformar sektioner",
        desc: "Utformar sektioner",
      },
      step5: {
        label: "Genererar rapport",
        desc: "Genererar rapport",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Bekräfta avbrott",
    confirm_abort_description: "Är du säker på att du vill avbryta processen?",
    keep_running: "Fortsätt köra",
    abort_run: "Avbryt process",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Svarsgenereringens förlopp",
    description:
      "Visar framdriften för uppgifter för att slutföra prompten, utifrån länkning till andra arbetsområden och filstorlekar. Fönstret stängs automatiskt när alla steg är klara.",
    step_fetching_memos: "Hämtar juridisk data om aktuella ämnen",
    step_processing_chunks: "Bearbetar uppladdade dokument",
    step_combining_responses: "Slutför svar",
    sub_step_chunk_label: "Bearbetar dokumentgrupp {{index}}",
    sub_step_memo_label: "Hämtade juridisk data från {{workspaceSlug}}",
    placeholder_sub_task: "Köad deluppgift",
    desc_fetching_memos:
      "Hämtar relevant juridisk information från länkade arbetsområden",
    desc_processing_chunks:
      "Analyserar och extraherar information från dokumentgrupper",
    desc_combining_responses:
      "Sammanställer information till ett omfattande svar",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Juridisk uppgift körs i bakgrunden.",
    dd: "Dokumentutkast fortsätter i bakgrunden.",
    reopen: "Öppna statusfönster",
  },
};
