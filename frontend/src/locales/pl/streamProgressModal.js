export default {
  cdbProgress: {
    "close-msg": "<PERSON>zy na pewno chcesz anulować proces?",
    general: {
      placeholderSubTask: "Przetwarzanie elementu {{index}}...",
    },
    main: {
      step1: {
        label: "Generuj listę sekcji",
        desc: "Wykorzystanie głównego dokumentu do utworzenia początkowej struktury.",
      },
      step2: {
        label: "Przetwarzanie dokumentów",
        desc: "Generowanie opisów i sprawdzanie istotności.",
      },
      step3: {
        label: "Przypisanie dokumentów do sekcji",
        desc: "Przydzielanie odpowiednich dokumentów do każdej sekcji.",
      },
      step4: {
        label: "Identyfikacja kwestii prawnych",
        desc: "Wyodrębnianie kluczowych kwestii prawnych dla każdej sekcji.",
      },
      step5: {
        label: "Generowanie notatek prawnych",
        desc: "Tworzenie notatek prawnych dla zidentyfikowanych kwestii.",
      },
      step6: {
        label: "Tworzenie sekcji",
        desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
      },
      step7: {
        label: "Łączenie i finalizacja dokumentu",
        desc: "Składanie sekcji w ostateczny dokument.",
      },
    },
    noMain: {
      step1: {
        label: "Przetwarzanie dokumentów",
        desc: "Generowanie opisów dla wszystkich przesłanych plików.",
      },
      step2: {
        label: "Generowanie listy sekcji",
        desc: "Tworzenie ustrukturyzowanej listy sekcji na podstawie podsumowań dokumentów.",
      },
      step3: {
        label: "Finalizacja mapowania dokumentów",
        desc: "Potwierdzanie istotności dokumentów dla każdej planowanej sekcji.",
      },
      step4: {
        label: "Identyfikacja kwestii prawnych",
        desc: "Wyodrębnianie kluczowych kwestii prawnych dla każdej sekcji.",
      },
      step5: {
        label: "Generowanie notatek prawnych",
        desc: "Tworzenie notatek prawnych dla zidentyfikowanych kwestii.",
      },
      step6: {
        label: "Tworzenie sekcji",
        desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
      },
      step7: {
        label: "Łączenie i finalizacja dokumentu",
        desc: "Składanie wszystkich sekcji w ostateczny dokument prawny.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Przetwarzanie plików referencyjnych",
        desc: "Przetwarzanie plików referencyjnych",
      },
      step2: {
        label: "Przetwarzanie plików przeglądowych",
        desc: "Przetwarzanie plików przeglądowych",
      },
      step3: {
        label: "Generowanie listy sekcji",
        desc: "Generowanie listy sekcji",
      },
      step4: {
        label: "Pisanie sekcji",
        desc: "Pisanie sekcji",
      },
      step5: {
        label: "Generowanie raportu",
        desc: "Generowanie raportu",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Potwierdź przerwanie",
    confirm_abort_description: "Czy na pewno chcesz anulować proces?",
    keep_running: "Kontynuuj działanie",
    abort_run: "Przerwij proces",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Postęp generowania odpowiedzi",
    description:
      "Wyświetla postęp w czasie zadań do zakończenia promptu, w zależności od połączenia z innymi obszarami roboczymi i rozmiarem plików. Modal zostanie automatycznie zamknięty po zakończeniu wszystkich kroków.",
    step_fetching_memos: "Pobieranie danych prawnych na bieżące tematy",
    step_processing_chunks: "Przetwarzanie przesłanych dokumentów",
    step_combining_responses: "Finalizacja odpowiedzi",
    sub_step_chunk_label: "Przetwarzanie grupy dokumentów {{index}}",
    sub_step_memo_label: "Pobrano dane prawne z {{workspaceSlug}}",
    placeholder_sub_task: "Krok w kolejce",
    desc_fetching_memos:
      "Pobieranie istotnych informacji prawnych z powiązanych obszarów roboczych",
    desc_processing_chunks:
      "Analizowanie i wyodrębnianie informacji z grup dokumentów",
    desc_combining_responses: "Synteza informacji w kompleksową odpowiedź",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Zadanie prawne działa w tle.",
    dd: "Tworzenie dokumentu kontynuowane w tle.",
    reopen: "Otwórz okno statusu",
  },
};
