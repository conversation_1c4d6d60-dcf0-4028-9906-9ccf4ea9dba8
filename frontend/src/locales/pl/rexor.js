export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Zarejestruj projekt Rexor",
    "project-id": "ID projektu",
    "resource-id": "ID zasobu",
    "activity-id": "ID aktywności",
    register: "Zarejestruj projekt",
    "invoice-text": "Liczba wyszukiwań Foynet",
    registering: "rejestrowanie...",
    "not-active": "Ta sprawa nie jest aktywna do rejestracji",
    account: {
      title: "<PERSON><PERSON>uj się do Rexor",
      username: "<PERSON>zwa użytkownika",
      password: "<PERSON>ł<PERSON>",
      "no-token": "Brak tokena otrzymanego w handleLoginSuccess",
      logout: "Wyloguj się",
      "no-user": "Proszę najpierw się zalogować",
      connected: "Połączono z Rexorem",
      "not-connected": "Nie połączono",
      "change-account": "Zmień konto",
      "session-expired": "Sesja wygasła. Proszę zaloguj się ponownie.",
    },
    "hide-article-transaction": "Ukryj formularz transakcji czasowej",
    "show-article-transaction": "Pokaż formularz transakcji czasowej",
    "article-transaction-title": "Dodaj transakcję czasową",
    "registration-date": "Data rejestracji",
    description: "Opis",
    "description-internal": "Opis wewnętrzny",
    "hours-worked": "Przepracowane godziny",
    "invoiced-hours": "Zafakturowane godziny",
    invoiceable: "Fakturowalne",
    "sending-article-transaction": "Wysyłanie transakcji czasowej...",
    "save-article-transaction": "Zapisz transakcję czasową",
    "project-not-register": "Projekt musi być najpierw zarejestrowany.",
    "article-transaction-error": "Nie udało się zapisać transakcji czasowej",
    "not-exist": "Nie można znaleźć tej sprawy",
    "missing-economy-id-admin":
      "Proszę dodać ID ekonomiczne do swojego profilu użytkownika przed zarejestrowaniem projektu.",
    "missing-economy-id-user":
      "Proszę poprosić administratora systemu o dodanie ID ekonomicznego do Twojego profilu użytkownika, aby móc zarejestrować projekt.",
    "api-settings": {
      title: "Konfiguracja API Rexor",
      description:
        "Skonfiguruj niestandardowe punkty końcowe API Rexor i dane uwierzytelniające. Pozostaw puste, aby użyć wartości domyślnych.",
      "api-base-url": "Bazowy URL API",
      "api-base-url-placeholder": "https://api.rexor.se/v231/Api",
      "auth-url": "URL uwierzytelniania",
      "auth-url-placeholder": "https://auth.rexor.se/v231/Token",
      "client-id-dev": "ID klienta deweloperskiego",
      "client-id-dev-placeholder": "testfoyen",
      "client-id-prod": "ID klienta produkcyjnego",
      "client-id-prod-placeholder": "foyen",
      "api-host": "Host API",
      "api-host-placeholder": "api.rexor.se",
      "save-button": "Zapisz ustawienia",
      "reset-button": "Przywróć domyślne",
      "success-message": "Ustawienia API Rexor zostały pomyślnie zapisane",
      "error-message": "Nie udało się zapisać ustawień API Rexor",
    },
  },
};
