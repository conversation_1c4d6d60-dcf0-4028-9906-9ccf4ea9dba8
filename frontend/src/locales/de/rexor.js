export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Rexor-Projekt registrieren",
    "project-id": "Projekt-ID",
    "resource-id": "Ressourcen-ID",
    "activity-id": "Aktivitäts-ID",
    register: "Projekt registrieren",
    "invoice-text": "Foynet Anzahl der Abfragen",
    registering: "Registrierung läuft...",
    "not-active": "Dieser Fall ist nicht aktiv für eine Registrierung",
    account: {
      title: "<PERSON><PERSON> an<PERSON>",
      username: "<PERSON><PERSON><PERSON><PERSON>",
      password: "Passwort",
      "no-token": "Kein <PERSON>ken in handleLoginSuccess erhalten",
      logout: "Abmelden",
      "no-user": "Bitte melde dich zuerst an",
      connected: "Mit Rexor verbunden",
      "not-connected": "Nicht verbunden",
      "change-account": "Konto wechseln",
      "session-expired": "Sitzung abgelaufen. Bitte melde dich erneut an.",
    },
    "hide-article-transaction": "Formular für Zeittransaktionen ausblenden",
    "show-article-transaction": "Formular für Zeittransaktionen anzeigen",
    "article-transaction-title": "Zeittransaktion hinzufügen",
    "registration-date": "Registrierungsdatum",
    description: "Beschreibung",
    "description-internal": "Interne Beschreibung",
    "hours-worked": "Gearbeitete Stunden",
    "invoiced-hours": "Berechnete Stunden",
    invoiceable: "Abrechenbar",
    "sending-article-transaction": "Zeittransaktion wird gesendet...",
    "save-article-transaction": "Zeittransaktion speichern",
    "project-not-register": "Projekt muss zuerst registriert werden.",
    "article-transaction-error": "Fehler beim Absenden der Zeittransaktion",
    "not-exist": "Dieser Fall konnte nicht gefunden werden",
    "missing-economy-id-admin":
      "Bitte fügen Sie eine Wirtschafts-ID zu Ihrem Benutzerprofil hinzu, bevor Sie ein Projekt registrieren.",
    "missing-economy-id-user":
      "Bitte bitten Sie den Systemadministrator, eine Wirtschafts-ID zu Ihrem Benutzerprofil hinzuzufügen, um ein Projekt registrieren zu können.",
    "api-settings": {
      title: "Rexor API-Konfiguration",
      description:
        "Konfigurieren Sie benutzerdefinierte Rexor API-Endpunkte und Anmeldedaten. Lassen Sie das Feld leer, um Standardwerte zu verwenden.",
      "api-base-url": "API-Basis-URL",
      "api-base-url-placeholder": "https://api.rexor.se/v231/Api",
      "auth-url": "Authentifizierungs-URL",
      "auth-url-placeholder": "https://auth.rexor.se/v231/Token",
      "client-id-dev": "Entwicklungs-Client-ID",
      "client-id-dev-placeholder": "testfoyen",
      "client-id-prod": "Produktions-Client-ID",
      "client-id-prod-placeholder": "foyen",
      "api-host": "API-Host",
      "api-host-placeholder": "api.rexor.se",
      "save-button": "Einstellungen speichern",
      "reset-button": "Auf Standard zurücksetzen",
      "success-message": "Rexor API-Einstellungen erfolgreich gespeichert",
      "error-message": "Fehler beim Speichern der Rexor API-Einstellungen",
    },
  },
};
