import { create } from "zustand";

const useCdbProgressStore = create((set, get) => ({
  // Thread-specific progress tracking: { [threadSlug]: { progressEvents, streamEvents, isProcessing, isModalOpen, flowType } }
  threadProgress: {},

  // Get thread-specific state
  getThreadState: (threadSlug) => {
    const state = get();
    const defaultThreadState = {
      progressEvents: [],
      streamEvents: [],
      isProcessing: false,
      isModalOpen: false,
      flowType: null,
      showProgressNotification: false,
    };
    return state.threadProgress[threadSlug] || defaultThreadState;
  },

  // Update specific thread state
  updateThreadState: (threadSlug, updates) => {
    set((state) => {
      const defaultThreadState = {
        progressEvents: [],
        streamEvents: [],
        isProcessing: false,
        isModalOpen: false,
        flowType: null,
        showProgressNotification: false,
      };
      const currentThreadState =
        state.threadProgress[threadSlug] || defaultThreadState;

      return {
        threadProgress: {
          ...state.threadProgress,
          [threadSlug]: {
            ...currentThreadState,
            ...updates,
          },
        },
      };
    });
  },

  // Modal management for specific thread
  openModal: (threadSlug) => {
    if (threadSlug == null) return;
    get().updateThreadState(threadSlug, { isModalOpen: true });
  },

  closeModal: (threadSlug) => {
    if (threadSlug == null) return;
    const threadState = get().getThreadState(threadSlug);
    get().updateThreadState(threadSlug, { isModalOpen: false });

    // If processing is ongoing when modal is closed, mark that a progress notification should be shown
    if (threadState.isProcessing) {
      get().updateThreadState(threadSlug, { showProgressNotification: true });
    }
  },

  // Processing management for specific thread
  startProcessing: (threadSlug, flowType = null) => {
    if (threadSlug == null) return;
    get().updateThreadState(threadSlug, {
      progressEvents: [],
      streamEvents: [],
      isProcessing: true,
      flowType,
    });
  },

  stopProcessing: (threadSlug) => {
    if (threadSlug == null) return;
    get().updateThreadState(threadSlug, { isProcessing: false });
  },

  // Progress notification management for specific thread
  showProgressNotification: (threadSlug) => {
    if (threadSlug == null) return;
    get().updateThreadState(threadSlug, { showProgressNotification: true });
  },

  hideProgressNotification: (threadSlug) => {
    if (threadSlug == null) return;
    get().updateThreadState(threadSlug, { showProgressNotification: false });
  },

  // Event management for specific thread
  addProgressEvent: (threadSlug, eventDetail) => {
    if (threadSlug == null) return;
    const threadState = get().getThreadState(threadSlug);
    get().updateThreadState(threadSlug, {
      progressEvents: [...threadState.progressEvents, eventDetail],
    });
  },

  addStreamEvent: (threadSlug, eventDetail) => {
    if (threadSlug == null) return;
    const threadState = get().getThreadState(threadSlug);
    get().updateThreadState(threadSlug, {
      streamEvents: [...threadState.streamEvents, eventDetail],
    });
  },

  // Reset everything for specific thread
  clearStoreAndStop: (threadSlug) => {
    if (threadSlug == null) return;
    get().updateThreadState(threadSlug, {
      progressEvents: [],
      streamEvents: [],
      isProcessing: false,
      isModalOpen: false,
      flowType: null,
      showProgressNotification: false,
    });
  },

  // Reset but keep processing state for specific thread (for when modal is closed but task continues)
  resetEventsOnly: (threadSlug) => {
    if (threadSlug == null) return;
    get().updateThreadState(threadSlug, {
      progressEvents: [],
      streamEvents: [],
    });
  },

  // Clean up threads that are no longer needed (optional cleanup method)
  cleanupThread: (threadSlug) => {
    if (threadSlug == null) return;
    set((state) => {
      const newThreadProgress = { ...state.threadProgress };
      delete newThreadProgress[threadSlug];
      return { threadProgress: newThreadProgress };
    });
  },
}));

// Thread-specific selector hooks for better performance
export const useCdbProgressEvents = (threadSlug) =>
  useCdbProgressStore(
    (state) => state.getThreadState(threadSlug).progressEvents
  );

export const useCdbStreamEvents = (threadSlug) =>
  useCdbProgressStore((state) => state.getThreadState(threadSlug).streamEvents);

export const useIsCdbProcessing = (threadSlug) =>
  useCdbProgressStore((state) => state.getThreadState(threadSlug).isProcessing);

export const useIsCdbModalOpen = (threadSlug) =>
  useCdbProgressStore((state) => state.getThreadState(threadSlug).isModalOpen);

export const useCdbFlowType = (threadSlug) =>
  useCdbProgressStore((state) => state.getThreadState(threadSlug).flowType);

export const useShowProgressNotification = (threadSlug) =>
  useCdbProgressStore(
    (state) => state.getThreadState(threadSlug).showProgressNotification
  );

export default useCdbProgressStore;
