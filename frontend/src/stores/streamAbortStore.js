import { create } from "zustand";

const useStreamAbortStore = create((set) => ({
  abortRequestTimestamp: null,
  isAbortInProgress: false,

  requestAbort: () => {
    set({ abortRequestTimestamp: Date.now(), isAbortInProgress: true });
  },

  acknowledgeAbort: () => {
    set({ isAbortInProgress: false });
  },
}));

export const useAbortRequestTimestamp = () =>
  useStreamAbortStore((state) => state.abortRequestTimestamp);

export const useIsAbortInProgress = () =>
  useStreamAbortStore((state) => state.isAbortInProgress);

export const useRequestAbort = () =>
  useStreamAbortStore((state) => state.requestAbort);

export const useAcknowledgeAbort = () =>
  useStreamAbortStore((state) => state.acknowledgeAbort);

export default useStreamAbortStore;
