import { create } from "zustand";
import useCdbProgressStore from "../cdbProgressStore";

describe("cdbProgressStore", () => {
  let store;

  beforeEach(() => {
    // Reset the store before each test
    store = useCdbProgressStore.getState();
    // Clear the thread progress
    store.threadProgress = {};
  });

  test("should handle empty string threadSlug without creating undefined keys", () => {
    const emptyThreadSlug = "";

    // Test openModal with empty string - should work correctly
    store.openModal(emptyThreadSlug);

    // Verify the state works correctly for empty string key
    const threadState = store.getThreadState(emptyThreadSlug);
    expect(threadState.isModalOpen).toBe(true);

    // Most importantly: verify no "undefined" or "null" keys were created
    expect(store.threadProgress["undefined"]).toBeUndefined();
    expect(store.threadProgress["null"]).toBeUndefined();

    // Test that we can also process events with empty string
    store.startProcessing(emptyThreadSlug, "main");
    store.addProgressEvent(emptyThreadSlug, { step: 1, progress: 50 });

    const updatedState = store.getThreadState(emptyThreadSlug);
    expect(updatedState.isProcessing).toBe(true);
    expect(updatedState.flowType).toBe("main");
    expect(updatedState.progressEvents).toHaveLength(1);

    // Verify still no undefined/null keys
    expect(store.threadProgress["undefined"]).toBeUndefined();
    expect(store.threadProgress["null"]).toBeUndefined();
  });

  test("should handle null/undefined threadSlug gracefully", () => {
    const initialThreadProgressKeys = Object.keys(store.threadProgress);

    // These should not create any new keys
    store.openModal(null);
    store.openModal(undefined);
    store.startProcessing(null);
    store.startProcessing(undefined);

    // Verify no new keys were added
    const finalThreadProgressKeys = Object.keys(store.threadProgress);
    expect(finalThreadProgressKeys).toEqual(initialThreadProgressKeys);
  });

  test("should work correctly with valid thread slugs", () => {
    const threadSlug = "test-thread-123";

    // Test the full workflow
    store.openModal(threadSlug);
    store.startProcessing(threadSlug, "main");
    store.addProgressEvent(threadSlug, { step: 1, progress: 50 });

    const threadState = store.getThreadState(threadSlug);
    expect(threadState.isModalOpen).toBe(true);
    expect(threadState.isProcessing).toBe(true);
    expect(threadState.flowType).toBe("main");
    expect(threadState.progressEvents).toHaveLength(1);
    expect(threadState.progressEvents[0]).toEqual({ step: 1, progress: 50 });
  });

  test("should handle progress notifications correctly", () => {
    const threadSlug = "test-thread";

    // Start processing and close modal
    store.startProcessing(threadSlug, "main");
    store.openModal(threadSlug);
    store.closeModal(threadSlug);

    const threadState = store.getThreadState(threadSlug);
    expect(threadState.isModalOpen).toBe(false);
    expect(threadState.showProgressNotification).toBe(true);
  });
});
