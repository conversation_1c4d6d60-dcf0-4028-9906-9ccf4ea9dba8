import React, { useState, useEffect, useCallback, useRef } from "react";
import { useTranslation } from "react-i18next";
import { AiOutlineLoading } from "react-icons/ai";
import { IoIosCheckmarkCircle, IoIosWarning } from "react-icons/io";
import useCdbProgressStore, {
  useCdbProgressEvents,
  useCdbStreamEvents,
  useIsCdbProcessing,
} from "@/stores/cdbProgressStore";

// Function to clean file names by removing UUIDs and file extensions
const cleanFileName = (filename) => {
  if (!filename) return "";

  try {
    // Make sure filename is a string
    const filenameStr = String(filename);

    // First, handle JSON files specifically (from server)
    if (filenameStr.endsWith(".json")) {
      // Remove .json extension
      let cleanName = filenameStr.replace(/\\.json$/, "");

      // Remove UUID pattern (common in JSON files)
      return cleanName;
    }

    // For other files, remove any file extension
    let cleanName = filenameStr.replace(/\\.[^/.]+$/, "");

    // Remove UUID pattern (8-4-4-4-12 format or similar)
    // This regex looks for patterns like -a1b2c3d4 or -a1b2-c3d4-etc
    cleanName = cleanName.replace(/-[0-9a-f]{4,}(?:-[0-9a-f]{4,}){0,4}$/i, "");

    // Also remove any standalone UUIDs that might be at the end
    cleanName = cleanName.replace(
      /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
      ""
    );

    return cleanName;
  } catch (error) {
    console.error("Error cleaning filename:", error, filename);
    return String(filename || "");
  }
};

const LegalProgress = ({ onComplete, threadSlug = "" }) => {
  const { t } = useTranslation();
  const progressEvents = useCdbProgressEvents(threadSlug);
  const streamEvents = useCdbStreamEvents(threadSlug);
  const isProcessing = useIsCdbProcessing(threadSlug);

  const [steps, setSteps] = useState([
    {
      id: 1,
      title: <span>{t("cdbProgress.main.step1.label")}</span>,
      description: <span>{t("cdbProgress.main.step1.desc")}</span>,
      progress: 0,
      status: "pending",
      isActive: true,
    },
    {
      id: 2,
      title: <span>{t("cdbProgress.main.step2.label")}</span>,
      description: <span>{t("cdbProgress.main.step2.desc")}</span>,
      progress: 0,
      status: "pending",
      isActive: false,
    },
    {
      id: 3,
      title: <span>{t("cdbProgress.main.step3.label")}</span>,
      description: <span>{t("cdbProgress.main.step3.desc")}</span>,
      progress: 0,
      status: "pending",
      isActive: false,
    },
    {
      id: 4,
      title: <span>{t("cdbProgress.main.step4.label")}</span>,
      description: <span>{t("cdbProgress.main.step4.desc")}</span>,
      progress: 0,
      status: "pending",
      isActive: false,
    },
    {
      id: 5,
      title: <span>{t("cdbProgress.main.step5.label")}</span>,
      description: <span>{t("cdbProgress.main.step5.desc")}</span>,
      progress: 0,
      status: "pending",
      isActive: false,
    },
    {
      id: 6,
      title: <span>{t("cdbProgress.main.step6.label")}</span>,
      description: <span>{t("cdbProgress.main.step6.desc")}</span>,
      progress: 0,
      status: "pending",
      isActive: false,
    },
    {
      id: 7,
      title: <span>{t("cdbProgress.main.step7.label")}</span>,
      description: <span>{t("cdbProgress.main.step7.desc")}</span>,
      progress: 0,
      status: "pending",
      isActive: false,
    },
  ]);

  const updateStepProgress = useCallback((stepId, newProgress) => {
    setSteps((prevSteps) =>
      prevSteps.map((step) => {
        if (step.id === stepId) {
          const prevStep = prevSteps.find((s) => s.id === stepId - 1);
          const prevCompleted = prevStep ? prevStep.progress >= 100 : true;

          const status =
            newProgress === -2
              ? "error"
              : newProgress >= 100
                ? "completed"
                : newProgress < 0
                  ? "pending"
                  : "pending";

          return {
            ...step,
            progress: newProgress,
            status,
            isActive: prevCompleted && newProgress < 100,
          };
        }
        // Auto-activate next step only when previous is fully completed
        if (step.id === stepId + 1 && newProgress >= 100) {
          return { ...step, isActive: true };
        }
        return step;
      })
    );
  }, []);

  // Utility to add or update a sub-step within a main step
  const addOrUpdateSubStep = useCallback(
    (parentId, subIndex, total, label, progress = 100) => {
      setSteps((prev) =>
        prev.map((s) => {
          if (s.id !== parentId) return s;

          // ensure subSteps array exists
          const subSteps = s.subSteps ? [...s.subSteps] : [];

          // Expand array to total length
          if (total && subSteps.length < total) {
            for (let k = subSteps.length; k < total; k++) {
              subSteps.push({
                id: k + 1,
                label: t("cdbProgress.general.placeholderSubTask", {
                  index: k + 1,
                }),
                status: "pending",
              });
            }
          }

          // Update the specific sub-step
          if (subSteps[subIndex - 1]) {
            const subStatus =
              progress === -2
                ? "error"
                : progress === -1
                  ? "loading"
                  : "completed";
            subSteps[subIndex - 1] = {
              ...subSteps[subIndex - 1],
              label: label || subSteps[subIndex - 1].label,
              status: subStatus,
            };
          }

          // Calculate overall progress by completed subs
          const completed = subSteps.filter(
            (ss) => ss.status === "completed"
          ).length;
          const newProgress = total
            ? Math.round((completed / total) * 100)
            : s.progress;

          return {
            ...s,
            subSteps,
            progress: newProgress,
            status: newProgress >= 100 ? "completed" : s.status,
            isActive: newProgress < 100,
          };
        })
      );
    },
    [t]
  );

  // Handler for granular progress events (memoized or defined outside if it doesn't depend on t directly for each call)
  const handleProgressEvent = useCallback(
    (detail) => {
      const { step, subStep, total, label, progress } = detail;
      if (!step) return;

      if (subStep && total) {
        if (label) {
          console.log(`Processing file: ${label} -> ${cleanFileName(label)}`);
        }
        addOrUpdateSubStep(step, subStep, total, label, progress);
      } else if (total && !subStep) {
        setSteps((prev) =>
          prev.map((s) => {
            if (s.id !== step) return s;
            const subStepsArray = s.subSteps ? [...s.subSteps] : [];
            if (subStepsArray.length < total) {
              for (let k = subStepsArray.length; k < total; k++) {
                subStepsArray.push({
                  id: k + 1,
                  label: t("cdbProgress.general.placeholderSubTask", {
                    index: k + 1,
                  }),
                  status: "pending",
                });
              }
            }
            return { ...s, subSteps: subStepsArray };
          })
        );
      }

      if (progress !== undefined) {
        updateStepProgress(step, progress);
      }
    },
    [addOrUpdateSubStep, updateStepProgress, t]
  );

  // Handler for stream events (memoized or defined outside)
  const handleCDBStreamEvent = useCallback(
    (data) => {
      if (data.type === "finalizeResponseStream") {
        for (let i = 1; i <= 7; i++) {
          updateStepProgress(i, 100);
        }
        if (onComplete) {
          setTimeout(onComplete, 500);
        }
      }
    },
    [updateStepProgress, onComplete]
  );

  // Refs to track processed events length
  const processedProgressEventsLengthRef = useRef(0);
  const processedStreamEventsLengthRef = useRef(0);

  useEffect(() => {
    const unsubscribe = useCdbProgressStore.subscribe((newState, prevState) => {
      if (!newState.isProcessing && prevState.isProcessing && onComplete) {
        const currentStreamEvents = useCdbProgressStore.getState().streamEvents;
        const hasFinalizeStream = currentStreamEvents.some(
          (event) => event.type === "finalizeResponseStream"
        );
        if (!hasFinalizeStream) {
          // Consider if onComplete should be called here or handled differently on abort
        }
      }
    });

    return () => {
      unsubscribe();
    };
  }, [onComplete, isProcessing]);

  useEffect(() => {
    if (progressEvents.length === 0) {
      processedProgressEventsLengthRef.current = 0;
    }
    const newEvents = progressEvents.slice(
      processedProgressEventsLengthRef.current
    );
    if (newEvents.length > 0) {
      newEvents.forEach(handleProgressEvent);
      processedProgressEventsLengthRef.current = progressEvents.length;
    }
  }, [progressEvents, handleProgressEvent]);

  useEffect(() => {
    if (streamEvents.length === 0) {
      processedStreamEventsLengthRef.current = 0;
    }
    const newEvents = streamEvents.slice(
      processedStreamEventsLengthRef.current
    );
    if (newEvents.length > 0) {
      newEvents.forEach(handleCDBStreamEvent);
      processedStreamEventsLengthRef.current = streamEvents.length;
    }
  }, [streamEvents, handleCDBStreamEvent]);

  const getStatusIcon = (status) => {
    switch (status) {
      case "completed":
        return <IoIosCheckmarkCircle className="text-green-500 w-5 h-5" />;
      case "loading":
        return (
          <AiOutlineLoading className="animate-spin text-blue-500 w-5 h-5" />
        );
      case "error":
        return <IoIosWarning className="text-red-500 w-5 h-5" />;
      default:
        return <IoIosCheckmarkCircle className="text-gray-400 w-5 h-5" />;
    }
  };

  return (
    <div className="progressContainer">
      <ul className="progress text-foreground">
        {steps.map((step) => (
          <li
            key={step.id}
            className={`progress__item list-none text-foreground ${
              step.status === "completed"
                ? "progress__item--completed"
                : step.isActive
                  ? "progress__item--active"
                  : "progress__item--inactive"
            } ${step.subSteps && step.subSteps.length > 0 ? "progress__item--has-subtasks" : ""}`}
          >
            <p className="progress__title">{step.title}</p>
            <small>{step.description}</small>
            <br />
            <small className="flex flex-row font-bold items-center gap-1">
              Status: {getStatusIcon(step.status)}
            </small>

            {/* Render sub-steps if they exist */}
            {step.subSteps && step.subSteps.length > 0 && (
              <ul className="progress-subtasks">
                {step.subSteps.map((ss) => (
                  <li key={ss.id} className="progress-subtask-item">
                    <div className="progress-subtask-icon">
                      {getStatusIcon(ss.status)}
                    </div>
                    <span className="progress-subtask-text">
                      {ss.label
                        ? cleanFileName(ss.label)
                        : t("cdbProgress.general.placeholderSubTask", {
                            index: ss.id,
                          })}
                    </span>
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default LegalProgress;
