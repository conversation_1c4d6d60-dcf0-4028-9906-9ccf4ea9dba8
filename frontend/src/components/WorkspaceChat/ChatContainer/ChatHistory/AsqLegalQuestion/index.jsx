import React, { useState, useEffect } from "react";
import { CgPerformance } from "react-icons/cg";
import Modal from "@/components/ui/Modal";
import AskLegalQuestModal from "@/components/Modals/AskLegalQuestion";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import { userFromStorage } from "@/utils/request";
import System from "@/models/system";
import {
  useSelectedFeatureCard,
  useSetSelectedFeatureCard,
} from "@/stores/userStore";
import useCdbProgressStore, {
  useIsCdbProcessing,
  useIsCdbModalOpen,
} from "@/stores/cdbProgressStore";
import { useParams } from "react-router-dom";

export default function PerformLegalQuestion({ sendCommand }) {
  const { t } = useTranslation();
  const [canPerformLegalTask, setCanPerformLegalTask] = useState(false);
  const user = userFromStorage();
  const selectedFeatureCard = useSelectedFeatureCard();
  const setSelectedFeatureCard = useSetSelectedFeatureCard();
  const { threadSlug = "" } = useParams();

  // Ensure threadSlug is always a string to prevent store pollution
  const safeThreadSlug = threadSlug || "";

  // Use thread-specific Zustand store
  const isModalOpen = useIsCdbModalOpen(safeThreadSlug);
  const isProcessing = useIsCdbProcessing(safeThreadSlug);
  const { openModal, closeModal } = useCdbProgressStore();

  useEffect(() => {
    const checkLegalTaskAccess = async () => {
      try {
        const response = await System.getPerformLegalTask();
        const isFeatureEnabled = response?.enabled;
        const isUserAccessEnabled = response?.allowUserAccess;

        // User can access if:
        // 1. They are admin/manager/superuser OR
        // 2. Feature is enabled AND user access is enabled for regular users
        const hasAccess =
          user?.role === "admin" ||
          user?.role === "manager" ||
          user?.role === "superuser" ||
          (isFeatureEnabled && isUserAccessEnabled);

        setCanPerformLegalTask(hasAccess);
      } catch (error) {
        console.error("Error checking legal task access:", error);
        setCanPerformLegalTask(false);
      }
    };

    checkLegalTaskAccess();
  }, [user?.role]);

  useEffect(() => {
    if (selectedFeatureCard === "complex-document-builder") {
      openModal(safeThreadSlug);
      setSelectedFeatureCard(null);
    }
  }, [selectedFeatureCard, setSelectedFeatureCard, openModal, safeThreadSlug]);

  if (!canPerformLegalTask) return null;

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        type="button"
        onClick={() => {
          // start a fresh legal task flow
          openModal(safeThreadSlug);
        }}
      >
        <CgPerformance />
        {t("performLegalTask.title")}
      </Button>
      <Modal
        isOpen={isModalOpen}
        onClose={() => closeModal(safeThreadSlug)}
        className="w-[400px] max-w-[90vw] max-h-[90vh] overflow-y-auto"
        title={t("performLegalTask.title")}
      >
        <div className="w-full flex flex-col">
          <AskLegalQuestModal
            sendCommand={sendCommand}
            onClose={() => closeModal(safeThreadSlug)}
            onProcessingChange={() => {}} // No longer needed as we use store state
            initialProcessing={isProcessing}
            threadSlug={safeThreadSlug}
          />
        </div>
      </Modal>
    </>
  );
}
