@keyframes burning-fuse {
  0% {
    box-shadow:
      0 0 0 0 rgba(255, 165, 0, 0.7),
      0 0 0 0 rgba(255, 165, 0, 0.7) inset;
  }
  50% {
    box-shadow:
      0 0 10px 5px rgba(255, 165, 0, 0.5),
      0 0 5px 2px rgba(255, 140, 0, 0.5) inset;
  }
  100% {
    box-shadow:
      0 0 0 0 rgba(255, 165, 0, 0),
      0 0 0 0 rgba(255, 165, 0, 0) inset;
  }
}

.animate-fuse {
  animation: burning-fuse 2s ease-out forwards;
  position: relative; /* Needed for pseudo-elements if you want to add a spark visual */
  overflow: hidden; /* Ensures the glow doesn't spill too much if not desired */
}

/* Optional: Add a small 'spark' element that travels around the button */
.animate-fuse::before {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 0 3px 1px yellow;
  animation: fuse-spark 2s linear forwards;
  opacity: 0; /* Start invisible, becomes visible during animation */
}

@keyframes fuse-spark {
  0% {
    opacity: 1;
    top: 0;
    left: 0;
  }
  25% {
    opacity: 1;
    top: 0;
    left: calc(100% - 4px);
  }
  50% {
    opacity: 1;
    top: calc(100% - 4px);
    left: calc(100% - 4px);
  }
  75% {
    opacity: 1;
    top: calc(100% - 4px);
    left: 0;
  }
  100% {
    opacity: 0;
    top: 0;
    left: 0;
  }
}
