import React, { useState, useEffect, useRef } from "react";
import { useNews } from "@/hooks/useNews";
import NewsModal from "@/components/NewsModal";
import useUser from "@/hooks/useUser";

export default function NewsProvider({ children }) {
  const { user } = useUser();
  const [showNewsModal, setShowNewsModal] = useState(false);
  const { unreadNews, hasUnreadNews, fetchUnreadNews } = useNews();
  const isDismissingRef = useRef(false);
  const recentlyClosedRef = useRef(false);
  const lastNewsCountRef = useRef(0);

  // Effect to handle showing news modal when unread news changes
  useEffect(() => {
    // Don't show modal if we're in the middle of dismissing news or recently closed
    if (isDismissingRef.current || recentlyClosedRef.current) {
      return;
    }

    // Only show modal if we have unread news and modal is not already open
    if (hasUnreadNews && !showNewsModal) {
      // Add a small delay to ensure state has settled
      const timer = setTimeout(() => {
        if (
          hasUnreadNews &&
          !isDismissingRef.current &&
          !recentlyClosedRef.current
        ) {
          setShowNewsModal(true);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [hasUnreadNews, showNewsModal]);

  // Track news count changes and reset recently closed flag when new news arrives
  useEffect(() => {
    // Check if new news arrived before updating the ref
    if (unreadNews.length > lastNewsCountRef.current) {
      recentlyClosedRef.current = false;
    }

    // Update the ref to current count
    lastNewsCountRef.current = unreadNews.length;
  }, [unreadNews.length]);

  const handleCloseNewsModal = () => {
    setShowNewsModal(false);
    // Set recently closed flag to prevent immediate reopening
    recentlyClosedRef.current = true;

    // Reset flags after a delay
    setTimeout(() => {
      isDismissingRef.current = false;
      // Keep recently closed flag for longer to prevent immediate reopening
      setTimeout(() => {
        recentlyClosedRef.current = false;
      }, 2000); // 2 seconds before allowing modal to reopen
    }, 500);
  };

  const handleNewsUpdate = () => {
    // Set flag to prevent modal from reopening immediately
    isDismissingRef.current = true;

    // Close the modal first
    setShowNewsModal(false);

    // Refresh news after a short delay to allow dismissal to process
    setTimeout(() => {
      fetchUnreadNews();

      // Reset the dismissing flag after news has been refreshed
      setTimeout(() => {
        isDismissingRef.current = false;
      }, 300);
    }, 200);
  };

  return (
    <>
      {children}
      <NewsModal
        isOpen={showNewsModal}
        onClose={handleCloseNewsModal}
        news={unreadNews}
        onNewsUpdate={handleNewsUpdate}
      />
    </>
  );
}
