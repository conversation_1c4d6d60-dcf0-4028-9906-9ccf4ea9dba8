import Modal from "@/components/ui/Modal";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import { useState } from "react";
import { LuChevronDown, LuChevronRight } from "react-icons/lu";
import { useTabNames } from "@/stores/settingsStore";

export default function UseGuideModal({ isOpen, onClose }) {
  const { t } = useTranslation();
  const tabNames = useTabNames();
  const [expandedSections, setExpandedSections] = useState({
    overview: true,
    modules: false,
    templateGeneration: false,
    documentBuilder: false,
    workspace: false,
    messageTools: false,
    promptInput: false,
    newsSystem: false,
    userAccount: false,
    // version: false,
    gettingStarted: false,
    tips: false,
  });

  // Get the fallback values for tab names
  const getTabName = (tabNameKey, translationKey) => {
    const tabName = tabNames[tabNameKey];
    if (tabName && tabName.trim() !== "") {
      return tabName;
    }
    return t(translationKey);
  };

  // Create interpolation values for translations
  const interpolationValues = {
    tabName1: getTabName("tabName1", "module.legal-qa"),
    tabName2: getTabName("tabName2", "module.document-drafting"),
  };

  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const SectionHeader = ({ sectionKey, title }) => (
    <button
      onClick={() => toggleSection(sectionKey)}
      className="flex items-center gap-2 w-full text-left p-3 bg-secondary hover:bg-secondary-hover rounded-lg transition-colors"
    >
      {expandedSections[sectionKey] ? (
        <LuChevronDown size={16} className="text-foreground" />
      ) : (
        <LuChevronRight size={16} className="text-foreground" />
      )}
      <h3 className="font-semibold text-foreground">{title}</h3>
    </button>
  );

  const FeatureList = ({ features, className = "" }) => (
    <ul className={`list-disc space-y-1 pl-6 ${className}`}>
      {features.map((feature, index) => (
        <li key={index} className="text-sm text-foreground/80">
          {feature}
        </li>
      ))}
    </ul>
  );

  const StepsList = ({ steps, className = "" }) => (
    <ol className={`list-decimal space-y-1 pl-6 ${className}`}>
      {steps.map((step, index) => (
        <li key={index} className="text-sm text-foreground/80">
          {step}
        </li>
      ))}
    </ol>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t("use-guide.title")}
      footer={<Button onClick={onClose}>{t("common.close")}</Button>}
      className="max-w-4xl max-h-[90vh]"
    >
      <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
        {/* Platform Overview */}
        <div>
          <SectionHeader
            sectionKey="overview"
            title={t("use-guide.overview.title")}
          />
          {expandedSections.overview && (
            <div className="mt-3 p-4 bg-background rounded-lg border">
              <p className="text-sm text-foreground/80 whitespace-pre-line">
                {t("use-guide.overview.description")}
              </p>
            </div>
          )}
        </div>

        {/* Platform Modules */}
        <div>
          <SectionHeader
            sectionKey="modules"
            title={t("use-guide.modules.title")}
          />
          {expandedSections.modules && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-4">
              <p className="text-sm text-foreground/80">
                {t("use-guide.modules.description")}
              </p>

              {/* Legal Q&A Module */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.modules.legalQA.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.modules.legalQA.description")}
                </p>
                <FeatureList
                  features={t("use-guide.modules.legalQA.features", {
                    returnObjects: true,
                  })}
                />
                <p className="text-sm text-foreground/70 mt-2 italic">
                  {t("use-guide.modules.legalQA.usage")}
                </p>
              </div>

              {/* Document Drafting Module */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.modules.documentDrafting.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.modules.documentDrafting.description")}
                </p>
                <FeatureList
                  features={t("use-guide.modules.documentDrafting.features", {
                    returnObjects: true,
                  })}
                />

                {/* Local AI Mode */}
                <div className="mt-3 p-3 bg-secondary rounded-lg">
                  <h5 className="font-medium text-foreground mb-1">
                    {t("use-guide.modules.documentDrafting.localAI.title")}
                  </h5>
                  <p className="text-sm text-foreground/80 mb-2">
                    {t(
                      "use-guide.modules.documentDrafting.localAI.description"
                    )}
                  </p>
                  <FeatureList
                    features={t(
                      "use-guide.modules.documentDrafting.localAI.benefits",
                      { returnObjects: true }
                    )}
                  />
                </div>

                <p className="text-sm text-foreground/70 mt-2 italic">
                  {t("use-guide.modules.documentDrafting.usage")}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Template Generation */}
        <div>
          <SectionHeader
            sectionKey="templateGeneration"
            title={t("use-guide.templateGeneration.title")}
          />
          {expandedSections.templateGeneration && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-4">
              <p className="text-sm text-foreground/80">
                {t("use-guide.templateGeneration.description")}
              </p>

              {/* Template System Overview */}
              <div className="border-l-4 border-emerald-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.templateGeneration.overview.title")}
                </h4>
                <p className="text-sm text-foreground/80">
                  {t("use-guide.templateGeneration.overview.description")}
                </p>
              </div>

              {/* Using Templates */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.templateGeneration.usingTemplates.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.templateGeneration.usingTemplates.description")}
                </p>
                <StepsList
                  steps={t(
                    "use-guide.templateGeneration.usingTemplates.steps",
                    {
                      returnObjects: true,
                    }
                  )}
                />
                <div className="mt-3">
                  <h5 className="font-medium text-foreground mb-1">Tips:</h5>
                  <FeatureList
                    features={t(
                      "use-guide.templateGeneration.usingTemplates.tips",
                      {
                        returnObjects: true,
                      }
                    )}
                  />
                </div>
              </div>

              {/* Custom Templates */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.templateGeneration.customTemplates.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-3">
                  {t(
                    "use-guide.templateGeneration.customTemplates.description"
                  )}
                </p>

                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t(
                        "use-guide.templateGeneration.customTemplates.creationProcess.title"
                      )}
                    </h5>
                    <StepsList
                      steps={t(
                        "use-guide.templateGeneration.customTemplates.creationProcess.steps",
                        {
                          returnObjects: true,
                        }
                      )}
                    />
                  </div>

                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t(
                        "use-guide.templateGeneration.customTemplates.management.title"
                      )}
                    </h5>
                    <FeatureList
                      features={t(
                        "use-guide.templateGeneration.customTemplates.management.features",
                        {
                          returnObjects: true,
                        }
                      )}
                    />
                  </div>

                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t(
                        "use-guide.templateGeneration.customTemplates.bestPractices.title"
                      )}
                    </h5>
                    <FeatureList
                      features={t(
                        "use-guide.templateGeneration.customTemplates.bestPractices.guidelines",
                        {
                          returnObjects: true,
                        }
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Benefits */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.templateGeneration.benefits.title")}
                </h4>
                <FeatureList
                  features={t(
                    "use-guide.templateGeneration.benefits.advantages",
                    {
                      returnObjects: true,
                    }
                  )}
                />
              </div>
            </div>
          )}
        </div>

        {/* Complex Document Builder */}
        <div>
          <SectionHeader
            sectionKey="documentBuilder"
            title={t("use-guide.documentBuilder.title")}
          />
          {expandedSections.documentBuilder && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-4">
              <p className="text-sm text-foreground/80">
                {t("use-guide.documentBuilder.description")}
              </p>

              {/* Overview */}
              <div className="border-l-4 border-indigo-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.documentBuilder.overview.title")}
                </h4>
                <p className="text-sm text-foreground/80">
                  {t("use-guide.documentBuilder.overview.description")}
                </p>
              </div>

              {/* Key Features */}
              <div className="border-l-4 border-cyan-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.documentBuilder.features.title")}
                </h4>
                <FeatureList
                  features={t(
                    "use-guide.documentBuilder.features.capabilities",
                    {
                      returnObjects: true,
                    }
                  )}
                />
              </div>

              {/* Usage Guide */}
              <div className="border-l-4 border-orange-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.documentBuilder.usage.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-3">
                  {t("use-guide.documentBuilder.usage.description")}
                </p>

                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t(
                        "use-guide.documentBuilder.usage.gettingStarted.title"
                      )}
                    </h5>
                    <StepsList
                      steps={t(
                        "use-guide.documentBuilder.usage.gettingStarted.steps",
                        {
                          returnObjects: true,
                        }
                      )}
                    />
                  </div>

                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t(
                        "use-guide.documentBuilder.usage.buildingContent.title"
                      )}
                    </h5>
                    <StepsList
                      steps={t(
                        "use-guide.documentBuilder.usage.buildingContent.steps",
                        {
                          returnObjects: true,
                        }
                      )}
                    />
                  </div>

                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t("use-guide.documentBuilder.usage.collaboration.title")}
                    </h5>
                    <FeatureList
                      features={t(
                        "use-guide.documentBuilder.usage.collaboration.capabilities",
                        {
                          returnObjects: true,
                        }
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Document Types */}
              <div className="border-l-4 border-pink-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.documentBuilder.documentTypes.title")}
                </h4>
                <FeatureList
                  features={t("use-guide.documentBuilder.documentTypes.types", {
                    returnObjects: true,
                  })}
                />
              </div>

              {/* Advanced Features */}
              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.documentBuilder.advancedFeatures.title")}
                </h4>
                <FeatureList
                  features={t(
                    "use-guide.documentBuilder.advancedFeatures.features",
                    {
                      returnObjects: true,
                    }
                  )}
                />
              </div>

              {/* Benefits */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.documentBuilder.benefits.title")}
                </h4>
                <FeatureList
                  features={t("use-guide.documentBuilder.benefits.advantages", {
                    returnObjects: true,
                  })}
                />
              </div>
            </div>
          )}
        </div>

        {/* Workspace Management */}
        <div>
          <SectionHeader
            sectionKey="workspace"
            title={t("use-guide.workspace.title")}
          />
          {expandedSections.workspace && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-4">
              <p className="text-sm text-foreground/80">
                {t("use-guide.workspace.description")}
              </p>

              {/* Creating Workspaces */}
              <div className="border-l-4 border-emerald-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.workspace.creating.title")}
                </h4>
                <StepsList
                  steps={t("use-guide.workspace.creating.steps", {
                    returnObjects: true,
                    ...interpolationValues,
                  })}
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-foreground mb-2">
                    {t("use-guide.workspace.managing.title")}
                  </h4>
                  <FeatureList
                    features={t("use-guide.workspace.managing.features", {
                      returnObjects: true,
                    })}
                  />
                </div>
              </div>

              <div>
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.workspace.uploading.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t(
                    "use-guide.workspace.uploading.description",
                    interpolationValues
                  )}
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t("use-guide.workspace.uploading.supportedFormatsTitle")}
                    </h5>
                    <FeatureList
                      features={t(
                        "use-guide.workspace.uploading.supportedFormats",
                        { returnObjects: true }
                      )}
                    />
                  </div>
                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t("use-guide.workspace.uploading.tipsTitle")}
                    </h5>
                    <FeatureList
                      features={t("use-guide.workspace.uploading.tips", {
                        returnObjects: true,
                      })}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Message Tools */}
        <div>
          <SectionHeader
            sectionKey="messageTools"
            title={t("use-guide.messageTools.title")}
          />
          {expandedSections.messageTools && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-3">
              <p className="text-sm text-foreground/80 mb-4">
                {t("use-guide.messageTools.description")}
              </p>

              <div className="grid gap-3">
                {[
                  "editMessage",
                  "copyMessage",
                  "regenerateResponse",
                  "deleteMessage",
                  "exportToWord",
                  "textToSpeech",
                  "citations",
                  "canvasChat",
                  "validateResponse",
                  "manualWorkEstimator",
                ].map((tool) => (
                  <div key={tool} className="p-3 bg-secondary rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-foreground">
                          {t(`use-guide.messageTools.${tool}.title`)}
                        </h4>
                        <p className="text-sm text-foreground/80 mt-1">
                          {t(`use-guide.messageTools.${tool}.description`)}
                        </p>
                        <p className="text-xs text-foreground/60 mt-1">
                          <span className="font-medium">Usage:</span>{" "}
                          {t(`use-guide.messageTools.${tool}.usage`)}
                        </p>
                        {t(`use-guide.messageTools.${tool}.note`, {
                          defaultValue: null,
                        }) && (
                          <p className="text-xs text-blue-600 mt-1">
                            <span className="font-medium">
                              {t("use-guide.labels.note")}:
                            </span>{" "}
                            {t(`use-guide.messageTools.${tool}.note`)}
                          </p>
                        )}
                        {t(`use-guide.messageTools.${tool}.availability`, {
                          defaultValue: null,
                        }) && (
                          <p className="text-xs text-orange-600 mt-1">
                            <span className="font-medium">
                              {t("use-guide.labels.availability")}:
                            </span>{" "}
                            {t(`use-guide.messageTools.${tool}.availability`)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Prompt Input Features */}
        <div>
          <SectionHeader
            sectionKey="promptInput"
            title={t("use-guide.promptInput.title")}
          />
          {expandedSections.promptInput && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-3">
              <p className="text-sm text-foreground/80 mb-4">
                {t("use-guide.promptInput.description")}
              </p>

              <div className="grid gap-3">
                {[
                  "speechToText",
                  "attachFiles",
                  "resetConversation",
                  "slashCommands",
                  "textSize",
                  "upgradePrompt",
                  "customAI",
                  "legalQuestion",
                  "importMemo",
                  "filesButton",
                  "legalTemplates",
                  "systemPrompt",
                  "deepSearch",
                  "submitPrompt",
                  "stopGeneration",
                ].map((feature) => (
                  <div key={feature} className="p-3 bg-secondary rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-foreground">
                          {t(`use-guide.promptInput.${feature}.title`)}
                        </h4>
                        <p className="text-sm text-foreground/80 mt-1">
                          {t(`use-guide.promptInput.${feature}.description`)}
                        </p>
                        <p className="text-xs text-foreground/60 mt-1">
                          <span className="font-medium">Usage:</span>{" "}
                          {t(`use-guide.promptInput.${feature}.usage`)}
                        </p>
                        {t(`use-guide.promptInput.${feature}.shortcut`, {
                          defaultValue: null,
                        }) && (
                          <p className="text-xs text-green-600 mt-1">
                            <span className="font-medium">
                              {t("use-guide.labels.shortcut")}:
                            </span>{" "}
                            {t(`use-guide.promptInput.${feature}.shortcut`)}
                          </p>
                        )}
                        {t(`use-guide.promptInput.${feature}.requirements`, {
                          defaultValue: null,
                        }) && (
                          <p className="text-xs text-orange-600 mt-1">
                            <span className="font-medium">
                              {t("use-guide.labels.requirements")}:
                            </span>{" "}
                            {t(`use-guide.promptInput.${feature}.requirements`)}
                          </p>
                        )}
                        {t(`use-guide.promptInput.${feature}.availability`, {
                          defaultValue: null,
                        }) && (
                          <p className="text-xs text-blue-600 mt-1">
                            <span className="font-medium">
                              {t("use-guide.labels.availability")}:
                            </span>{" "}
                            {t(`use-guide.promptInput.${feature}.availability`)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* News and Updates System */}
        <div>
          <SectionHeader
            sectionKey="newsSystem"
            title={t("use-guide.newsSystem.title")}
          />
          {expandedSections.newsSystem && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-4">
              <p className="text-sm text-foreground/80">
                {t("use-guide.newsSystem.description")}
              </p>

              {/* News System Overview */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.newsSystem.overview.title")}
                </h4>
                <p className="text-sm text-foreground/80">
                  {t("use-guide.newsSystem.overview.description")}
                </p>
              </div>

              {/* News Indicator */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.newsSystem.newsIndicator.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.newsSystem.newsIndicator.description")}
                </p>
                <FeatureList
                  features={t("use-guide.newsSystem.newsIndicator.features", {
                    returnObjects: true,
                  })}
                />
                <p className="text-sm text-foreground/70 mt-2 italic">
                  {t("use-guide.newsSystem.newsIndicator.usage")}
                </p>
              </div>

              {/* News Header Item */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.newsSystem.newsHeaderItem.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.newsSystem.newsHeaderItem.description")}
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-foreground">
                      Icon:
                    </span>
                    <span className="text-sm text-foreground/80">
                      {t("use-guide.newsSystem.newsHeaderItem.icon")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-foreground">
                      Usage:
                    </span>
                    <span className="text-sm text-foreground/80">
                      {t("use-guide.newsSystem.newsHeaderItem.usage")}
                    </span>
                  </div>
                </div>
                <div className="mt-3">
                  <h5 className="font-medium text-foreground mb-1">
                    Features:
                  </h5>
                  <FeatureList
                    features={t(
                      "use-guide.newsSystem.newsHeaderItem.features",
                      {
                        returnObjects: true,
                      }
                    )}
                  />
                </div>
                <p className="text-sm text-foreground/70 mt-2 italic">
                  <strong>{t("use-guide.labels.note")}:</strong>{" "}
                  {t("use-guide.newsSystem.newsHeaderItem.note")}
                </p>
                <p className="text-sm text-foreground/70 mt-1 italic">
                  <strong>{t("use-guide.labels.availability")}:</strong>{" "}
                  {t("use-guide.newsSystem.newsHeaderItem.availability")}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* User Account & Settings */}
        <div>
          <SectionHeader
            sectionKey="userAccount"
            title={t("use-guide.userAccount.title")}
          />
          {expandedSections.userAccount && (
            <div className="mt-3 p-4 bg-background rounded-lg border space-y-4">
              <p className="text-sm text-foreground/80">
                {t("use-guide.userAccount.description")}
              </p>

              {/* Account Settings */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.userAccount.accountSettings.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.userAccount.accountSettings.description")}
                </p>
                <FeatureList
                  features={t(
                    "use-guide.userAccount.accountSettings.features",
                    { returnObjects: true }
                  )}
                />
                <p className="text-sm text-foreground/70 mt-2 italic">
                  {t("use-guide.userAccount.accountSettings.access")}
                </p>
              </div>

              {/* Style Alignment */}
              <div className="border-l-4 border-indigo-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.userAccount.styleAlignment.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.userAccount.styleAlignment.description")}
                </p>
                <p className="text-sm text-foreground/80 mb-3">
                  {t("use-guide.userAccount.styleAlignment.overview")}
                </p>

                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t("use-guide.userAccount.styleAlignment.features.title")}
                    </h5>
                    <FeatureList
                      features={t(
                        "use-guide.userAccount.styleAlignment.features.list",
                        { returnObjects: true }
                      )}
                    />
                  </div>

                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t("use-guide.userAccount.styleAlignment.howToUse.title")}
                    </h5>
                    <StepsList
                      steps={t(
                        "use-guide.userAccount.styleAlignment.howToUse.steps",
                        { returnObjects: true }
                      )}
                    />
                  </div>

                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t(
                        "use-guide.userAccount.styleAlignment.management.title"
                      )}
                    </h5>
                    <FeatureList
                      features={t(
                        "use-guide.userAccount.styleAlignment.management.features",
                        { returnObjects: true }
                      )}
                    />
                  </div>

                  <div>
                    <h5 className="font-medium text-foreground mb-1">
                      {t("use-guide.userAccount.styleAlignment.tips.title")}
                    </h5>
                    <FeatureList
                      features={t(
                        "use-guide.userAccount.styleAlignment.tips.list",
                        { returnObjects: true }
                      )}
                    />
                  </div>
                </div>

                <p className="text-sm text-foreground/70 mt-2 italic">
                  {t("use-guide.userAccount.styleAlignment.access")}
                </p>
              </div>

              {/* Language Settings */}
              <div className="border-l-4 border-teal-500 pl-4">
                <h4 className="font-medium text-foreground mb-2">
                  {t("use-guide.userAccount.languageSettings.title")}
                </h4>
                <p className="text-sm text-foreground/80 mb-2">
                  {t("use-guide.userAccount.languageSettings.description")}
                </p>
                <FeatureList
                  features={t(
                    "use-guide.userAccount.languageSettings.supportedLanguages",
                    { returnObjects: true }
                  )}
                />
                <p className="text-sm text-foreground/70 mt-2 italic">
                  {t("use-guide.userAccount.languageSettings.usage")}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Getting Started */}
        <div>
          <SectionHeader
            sectionKey="gettingStarted"
            title={t("use-guide.gettingStarted.title")}
          />
          {expandedSections.gettingStarted && (
            <div className="mt-3 p-4 bg-background rounded-lg border">
              <StepsList
                steps={t("use-guide.gettingStarted.steps", {
                  returnObjects: true,
                  ...interpolationValues,
                })}
              />
            </div>
          )}
        </div>

        {/* Tips and Best Practices */}
        <div>
          <SectionHeader sectionKey="tips" title={t("use-guide.tips.title")} />
          {expandedSections.tips && (
            <div className="mt-3 p-4 bg-background rounded-lg border">
              <FeatureList
                features={t("use-guide.tips.list", { returnObjects: true })}
              />
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
}
