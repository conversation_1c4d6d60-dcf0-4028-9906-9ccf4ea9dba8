import React from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";

// Confirmation modal for aborting CDB legal task
const AbortWarningModal = ({ onCancel, onAbort }) => {
  const { t } = useTranslation();
  return (
    <Modal
      isOpen={true}
      onClose={onCancel}
      title={t("streamcdb_progress_modal.confirm_abort_title")}
      description={t("streamcdb_progress_modal.confirm_abort_description")}
      className="w-[400px] max-w-[90vw]"
    >
      <div className="flex justify-end space-x-2">
        <Button variant="secondary" onClick={onCancel}>
          {t("streamcdb_progress_modal.keep_running")}
        </Button>
        <Button variant="destructive" onClick={onAbort}>
          {t("streamcdb_progress_modal.abort_run")}
        </Button>
      </div>
    </Modal>
  );
};

export default AbortWarningModal;
