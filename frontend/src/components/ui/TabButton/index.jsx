import React, { useContext } from "react";
import { NavLink } from "react-router-dom";
import { cn } from "@/utils/classes";
import { Button } from "@/components/Button";
import { ThemeContext } from "@/context";

/**
 * TabButton - A reusable component for tab navigation or tab buttons
 *
 * @param {Object} props - Component props
 * @param {boolean} props.active - Whether the tab is active
 * @param {Function} props.onClick - Click handler function
 * @param {string} props.to - Path for NavLink (when used for navigation)
 * @param {React.ReactNode} props.icon - Optional icon to display before the text
 * @param {string} props.className - Additional classes
 * @param {boolean} props.visible - Whether the tab should be visible (defaults to true)
 * @param {React.ReactNode} props.children - Tab content/label
 */
export const TabButton = ({
  active,
  onClick,
  to,
  icon,
  className,
  visible = true,
  children,
  ...props
}) => {
  const { theme } = useContext(ThemeContext);
  if (!visible) return null;

  const baseStyles =
    "border-b-2 border-transparent rounded-none hover:bg-transparent";

  const getActiveStyles = (isActive) => {
    return isActive
      ? theme === "dark"
        ? "text-white border-b-white"
        : "text-primary border-b-primary"
      : "opacity-60 hover:opacity-100";
  };

  const tabStyles = cn(baseStyles, getActiveStyles(active), className);

  if (to) {
    return (
      <NavLink
        to={to}
        className={({ isActive }) =>
          cn(
            baseStyles,
            "flex gap-x-2 items-center pb-4",
            getActiveStyles(isActive),
            className
          )
        }
        {...props}
      >
        {icon && icon}
        {children}
      </NavLink>
    );
  }

  return (
    <Button onClick={onClick} variant="ghost" className={tabStyles} {...props}>
      {icon && icon}
      {children}
    </Button>
  );
};

export default TabButton;
