const prisma = require("../utils/prisma");
const { t } = require("../utils/i18n");

process.env.NODE_ENV === "development"
  ? require("dotenv").config({ path: `.env.${process.env.NODE_ENV}` })
  : require("dotenv").config();

const { default: slugify } = require("slugify");
const { isValidUrl, safeJsonParse } = require("../utils/http");
const { v4 } = require("uuid");
const { MetaGenerator } = require("../utils/boot/MetaGenerator");

function isNullOrNaN(value) {
  if (value === null) return true;
  return isNaN(value);
}

const SystemSettings = {
  protectedFields: [
    "multi_user_mode",
    "public_user_mode",
    "document_drafting",
    "document_drafting_linking",
    "qura",
    "copyOption",
    "citation",
    "document_drafting_prompt",
    "default_prompt",
    "websiteLink",
    "displayText",
    "vector_search_top_n",
    "keep_pdr_vectors",
    "tabName1",
    "tabName2",
    "tabName3",
    "prompt_output_logging",
    "max_tokens_per_user",
    "adjacent_vector_limit",
    "validation_prompt",
    "dd_vector_enabled",
    "dd_memo_enabled",
    "dd_base_enabled",
    "dd_linked_workspace_impact",
    "dd_vector_token_limit",
    "dd_memo_token_limit",
    "dd_base_token_limit",
    "performLegalTask",
    "feedback_enabled",
    "global_pdr_override",
    "legalTaskType",
    "canvas_system_prompt",
    "canvas_upload_system_prompt",
    "manual_work_estimator_prompt",
    "document_drafting_combine_prompt",
    "document_drafting_memo_prompt",
    "cdb_document_summary_system_prompt",
    "cdb_document_summary_user_prompt",
    "cdb_document_relevance_system_prompt",
    "cdb_document_relevance_user_prompt",
    "cdb_section_drafting_system_prompt",
    "cdb_section_drafting_user_prompt",
    "cdb_memo_creation_prompt",
    "cdb_memo_creation_prompt_template",
    "cdb_section_legal_issues_system_prompt",
    "cdb_section_legal_issues_user_prompt",
    "cdb_select_main_document_system_prompt",
    "cdb_select_main_document_user_prompt",
    "cdb_section_list_from_main_system_prompt",
    "cdb_section_list_from_main_user_prompt",
    "cdb_section_list_from_summaries_system_prompt",
    "cdb_section_list_from_summaries_user_prompt",
    "cdb_section_index_system_prompt",
    "cdb_reference_files_description_system_prompt",
    "cdb_reference_files_description_user_prompt",
    "cdb_review_files_description_system_prompt",
    "cdb_review_files_description_user_prompt",
    "cdb_reference_review_sections_system_prompt",
    "cdb_reference_review_sections_user_prompt",
    "cdb_reference_section_drafting_system_prompt",
    "cdb_reference_section_drafting_user_prompt",
    "dynamic_context_window_percentage",
    "custom_dynamic_context_window_percentage",
    "welcome_message_headline",
    "welcome_message_text",
    "login_ui",
    "request_legal_assistance_enabled",
    "request_legal_assistance_law_firm_name",
    "request_legal_assistance_email",
  ],
  publicFields: [
    "limit_user_messages",
    "message_limit",
    "footer_data",
    "support_email",
    "text_splitter_chunk_size",
    "text_splitter_chunk_overlap",
    "max_embed_chunk_size",
    "text_splitter_method",
    "text_splitter_jina_max_tokens",
    "text_splitter_jina_return_tokens",
    "text_splitter_jina_return_chunks",
    "agent_search_provider",
    "agent_sql_connections",
    "default_agent_skills",
    "imported_agent_skills",
    "custom_app_name",
    "feature_flags",
    "meta_page_title",
    "meta_page_favicon",
    "rerank_vector_count",
    "enable_lancedb_rerank",
    "university_mode",
  ],
  supportedFields: [
    "limit_user_messages",
    "message_limit",
    "logo_light",
    "logo_dark",
    "telemetry_id",
    "footer_data",
    "support_email",
    "text_splitter_chunk_size",
    "text_splitter_chunk_overlap",
    "text_splitter_method",
    "text_splitter_jina_max_tokens",
    "text_splitter_jina_return_tokens",
    "text_splitter_jina_return_chunks",
    "agent_search_provider",
    "default_agent_skills",
    "agent_sql_connections",
    "custom_app_name",
    "language",
    "palette",
    "experimental_live_file_sync",
    "meta_page_title",
    "dynamic_context_window_percentage",
    "meta_page_favicon",
    "custom_paragraph_text",
    "max_tokens_per_user",
    "invoice",
    "forced_invoice_logging",
    "rerank_vector_count",
    "rexor_linkage",
    "rexor_api_base_url",
    "rexor_auth_url",
    "rexor_client_id_dev",
    "rexor_client_id_prod",
    "rexor_api_host",
    "enable_lancedb_rerank",
    "docx_system_template",
    "docx_system_template_display",
    "deep_search_provider",
    "deep_search_model_id",
    "deep_search_api_key",
    "deep_search_enabled",
    "deep_search_context_percentage",
    "custom_model_reference",
    "custom_model_reference_2",
    "custom_model_reference_3",
    "custom_model_reference_4",
    "custom_model_reference_5",
    "custom_model_reference_6",
    "custom_dynamic_context_window_percentage",
    "custom_dynamic_context_window_percentage_2",
    "custom_dynamic_context_window_percentage_3",
    "custom_dynamic_context_window_percentage_4",
    "custom_dynamic_context_window_percentage_5",
    "custom_dynamic_context_window_percentage_6",
    "CustomUserAiStatus_1",
    "CustomUserAiStatus_2",
    "CustomUserAiStatus_3",
    "CustomUserAiStatus_4",
    "CustomUserAiStatus_5",
    "CustomUserAiStatus_6",
    "custom_legal_templates",
    "disableValidationPrompt",
    "university_mode",
  ],
  validations: {
    login_ui: (value) => {
      const validOptions = [
        "ist-legal-rwanda",
        "tender-flow",
        "ist-legal-general",
      ];
      if (!validOptions.includes(value)) {
        throw new Error(`Invalid login_ui value: ${value}`);
      }
      return value;
    },
    deep_search_context_percentage: (update) => {
      try {
        const numValue = Number(update);
        if (isNaN(numValue) || numValue < 5 || numValue > 20) {
          console.warn(
            `Invalid deep_search_context_percentage value: ${update}, defaulting to 15`
          );
          return 15;
        }
        return numValue;
      } catch (e) {
        console.error(
          `Failed to validate deep_search_context_percentage:`,
          e.message
        );
        return 15; // Safe default within valid range
      }
    },
    footer_data: (updates) => {
      try {
        const array = JSON.parse(updates)
          .filter((setting) => isValidUrl(setting.url))
          .slice(0, 3); // max of 3 items in footer.
        return JSON.stringify(array);
      } catch (e) {
        console.error(`Failed to run validation function on footer_data`);
        return JSON.stringify([]);
      }
    },
    text_splitter_chunk_size: (update) => {
      try {
        if (isNullOrNaN(update))
          throw new Error(t("errors.validation.value-not-number"));
        if (Number(update) <= 0)
          throw new Error(t("errors.validation.value-must-be-nonzero"));
        return Number(update);
      } catch (e) {
        console.error(
          `Failed to run validation function on text_splitter_chunk_size`,
          e.message
        );
        return 1000;
      }
    },
    text_splitter_chunk_overlap: (update) => {
      try {
        if (isNullOrNaN(update))
          throw new Error(t("errors.validation.value-not-number"));
        if (Number(update) < 0)
          throw new Error(t("errors.validation.value-cannot-be-negative"));
        return Number(update);
      } catch (e) {
        console.error(
          `Failed to run validation function on text_splitter_chunk_overlap`,
          e.message
        );
        return 20;
      }
    },
    text_splitter_method: (value) => {
      try {
        const validMethods = ["native", "jina"];
        return validMethods.includes(value) ? value : "native";
      } catch (e) {
        console.error("Failed to validate text_splitter_method", e.message);
        return "native";
      }
    },
    text_splitter_jina_max_tokens: (update) => {
      try {
        if (isNullOrNaN(update))
          throw new Error("text_splitter_jina_max_tokens must be a number");
        const num = Number(update);
        if (num < 1) {
          throw new Error("text_splitter_jina_max_tokens must be >= 1");
        }
        if (num > 2000) {
          throw new Error("text_splitter_jina_max_tokens cannot exceed 2000");
        }
        return num;
      } catch (e) {
        console.error("Invalid text_splitter_jina_max_tokens:", e.message);
        // fallback
        return 1000;
      }
    },
    agent_search_provider: (update) => {
      try {
        if (update === "none") return null;
        if (
          ![
            "google-search-engine",
            "searchapi",
            "serper-dot-dev",
            "bing-search",
            "serply-engine",
            "searxng-engine",
            "tavily-search",
          ].includes(update)
        )
          throw new Error(t("errors.validation.invalid-serp-provider"));
        return String(update);
      } catch (e) {
        console.error(
          `Failed to run validation function on agent_search_provider`,
          e.message
        );
        return null;
      }
    },
    default_agent_skills: (updates) => {
      try {
        const skills = updates.split(",").filter((skill) => !!skill);
        return JSON.stringify(skills);
      } catch (e) {
        console.error(`Could not validate agent skills.`);
        return JSON.stringify([]);
      }
    },
    agent_sql_connections: async (updates) => {
      const existingConnections = safeJsonParse(
        (await SystemSettings.get({ label: "agent_sql_connections" }))?.value,
        []
      );
      try {
        const updatedConnections = mergeConnections(
          existingConnections,
          safeJsonParse(updates, [])
        );
        return JSON.stringify(updatedConnections);
      } catch (e) {
        console.error(`Failed to merge connections`);
        return JSON.stringify(existingConnections ?? []);
      }
    },
    experimental_live_file_sync: (update) => {
      if (typeof update === "boolean")
        return update === true ? "enabled" : "disabled";
      if (!["enabled", "disabled"].includes(update)) return "disabled";
      return String(update);
    },
    meta_page_title: (newTitle) => {
      try {
        if (typeof newTitle !== "string" || !newTitle) return null;
        return String(newTitle);
      } catch {
        return null;
      } finally {
        new MetaGenerator().clearConfig();
      }
    },
    meta_page_favicon: (faviconUrl) => {
      if (!faviconUrl) return null;
      try {
        const url = new URL(faviconUrl);
        return url.toString();
      } catch {
        return null;
      } finally {
        new MetaGenerator().clearConfig();
      }
    },
    custom_paragraph_text: (update) => {
      return update ? String(update) : null;
    },
    websiteLink: (url) => {
      try {
        const parsedUrl = new URL(url);
        return parsedUrl.href; // Returns the full validated URL
      } catch (e) {
        console.error(`Failed to validate websiteLink: ${e.message}`);
        return null; // Return null if URL is invalid
      }
    },
    displayText: (text) => {
      if (typeof text === "string" && text.trim().length > 0) {
        return text.trim();
      } else {
        console.error(
          `Invalid displayText: '${text}' is empty or not a valid string.`
        );
        return null;
      }
    },
    tabName1: (text) => {
      if (typeof text === "string" && text.trim().length > 0) {
        return text.trim();
      } else {
        console.error(
          `Invalid tabName1: '${text}' is empty or not a valid string.`
        );
        return null;
      }
    },
    tabName2: (text) => {
      if (typeof text === "string" && text.trim().length > 0) {
        return text.trim();
      } else {
        console.error(
          `Invalid tabName2: '${text}' is empty or not a valid string.`
        );
        return null;
      }
    },
    tabName3: (text) => {
      if (typeof text === "string" && text.trim().length > 0) {
        return text.trim();
      } else {
        console.error(
          `Invalid tabName3: '${text}' is empty or not a valid string.`
        );
        return null;
      }
    },
    max_tokens_per_user: function (value) {
      const numValue = parseInt(value);
      if (isNaN(numValue) || numValue < 1) {
        throw new Error("Maximum tokens per user must be at least 1");
      }
      if (numValue > 10) {
        throw new Error("Maximum tokens per user cannot exceed 10");
      }
      return numValue;
    },
    text_splitter_jina_return_tokens: (update) => {
      try {
        return update === true || update === "true" || update === "on";
      } catch (e) {
        console.error("Invalid text_splitter_jina_return_tokens:", e.message);
        return true; // default to true
      }
    },
    text_splitter_jina_return_chunks: (update) => {
      try {
        return update === true || update === "true" || update === "on";
      } catch (e) {
        console.error("Invalid text_splitter_jina_return_chunks:", e.message);
        return true; // default to true
      }
    },
    rerank_vector_count: function (value) {
      try {
        const numValue = parseInt(value);
        if (isNaN(numValue) || numValue < 10) {
          console.warn(
            `Invalid rerank_vector_count value: ${value}, must be at least 10. Defaulting to 10.`
          );
          return 10;
        }
        if (numValue > 200) {
          console.warn(
            `Invalid rerank_vector_count value: ${value}, cannot exceed 200. Defaulting to 200.`
          );
          return 200;
        }
        return numValue;
      } catch (e) {
        console.error(`Failed to validate rerank_vector_count:`, e.message);
        return 50; // Safe default within valid range
      }
    },
    global_pdr_override: (update) => {
      // If not explicitly set to false, default to true
      return update !== false && update !== "false";
    },
    enable_lancedb_rerank: (update) => {
      try {
        if (typeof update !== "string" || !["on", "off"].includes(update)) {
          console.warn(
            `Invalid enable_lancedb_rerank value: ${update}, defaulting to "off"`
          );
          return "off";
        }
        return update;
      } catch (e) {
        console.error(`Failed to validate enable_lancedb_rerank:`, e.message);
        return "off";
      }
    },
    custom_model_reference: (update) => {
      if (typeof update !== "string") return "";
      return update.trim();
    },
    custom_legal_templates: (value) => {
      if (typeof value !== "string") return JSON.stringify([]);
      try {
        // If the value is already an array, stringify it.
        // if it is a string of an array, it is fine.
        // if it is some other string, then make it an empty array.
        const parsed = JSON.parse(value);
        if (Array.isArray(parsed)) return value; // already a stringified array.
        return JSON.stringify([]);
      } catch (e) {
        // If parsing fails, check if it's already a valid JSON array string (edge case)
        if (value.startsWith("[") && value.endsWith("]")) {
          try {
            JSON.parse(value); // verify it's valid JSON
            return value;
          } catch (e) {
            // fall through to return empty array string
          }
        }
        return JSON.stringify([]);
      }
    },
    dynamic_context_window_percentage: (update) => {
      const numValue = Number(update);
      if (isNaN(numValue) || numValue < 0 || numValue > 100) {
        console.warn(
          `Invalid dynamic_context_window_percentage value: ${update}, defaulting to 70`
        );
        return 70;
      }
      return numValue;
    },
    custom_dynamic_context_window_percentage: (update) => {
      if (update === null || update === "") return null;
      const numValue = Number(update);
      if (isNaN(numValue) || numValue < 0 || numValue > 100) {
        console.warn(
          `Invalid custom_dynamic_context_window_percentage value: ${update}, defaulting to 70`
        );
        return 70;
      }
      return numValue;
    },
    request_legal_assistance_enabled: (update) => {
      return update === true || update === "true";
    },
    request_legal_assistance_law_firm_name: (text) => {
      if (typeof text === "string" && text.trim().length > 0) {
        return text.trim();
      }
      return null;
    },
    request_legal_assistance_email: (email) => {
      if (typeof email === "string" && email.trim().length > 0) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailRegex.test(email.trim())) {
          return email.trim();
        }
      }
      return "";
    },
    disableValidationPrompt: (value) => {
      return value === "true" || value === true ? "true" : "false";
    },
    university_mode: (value) => {
      return value === "true" || value === true ? "true" : "false";
    },
    docx_system_template: (value) => {
      if (value === null) return null;
      if (typeof value !== "string") {
        throw new Error("docx_system_template must be a string filename");
      }
      const lower = value.toLowerCase();
      if (!lower.endsWith(".docx")) {
        throw new Error("Template must be a .docx file");
      }
      return value;
    },
    docx_system_template_display: (value) => {
      if (value === null) return null;
      if (typeof value !== "string") return null;
      return value.trim();
    },
    rexor_linkage: (value) => {
      return value === "true" || value === true ? "true" : "false";
    },
  },
  currentSettings: async function () {
    const { hasVectorCachedFiles } = require("../utils/files");

    const [status1, status2, status3, status4, status5, status6] =
      await Promise.all([
        SystemSettings.getValueOrFallback({ label: "CustomUserAiStatus_1" }),
        SystemSettings.getValueOrFallback({ label: "CustomUserAiStatus_2" }),
        SystemSettings.getValueOrFallback({ label: "CustomUserAiStatus_3" }),
        SystemSettings.getValueOrFallback({ label: "CustomUserAiStatus_4" }),
        SystemSettings.getValueOrFallback({ label: "CustomUserAiStatus_5" }),
        SystemSettings.getValueOrFallback({ label: "CustomUserAiStatus_6" }),
      ]);

    return {
      // --------------------------------------------------------
      // General Settings
      // --------------------------------------------------------
      RequiresAuth: !!process.env.AUTH_TOKEN,
      AuthToken: !!process.env.AUTH_TOKEN,
      JWTSecret: !!process.env.JWT_SECRET,
      StorageDir: process.env.STORAGE_DIR,
      MultiUserMode: await this.isMultiUserMode(),
      PublicUserMode: await this.isPublicUserMode(),
      DocumentDrafting: await this.isDocumentDrafting(),
      Qura: await this.isQura(),
      DisableTelemetry: process.env.DISABLE_TELEMETRY || "false",
      prompt_upgrade_template:
        (await this.get({ label: "prompt_upgrade_template" }))?.value || "",
      chat_deletion_count:
        (await this.get({ label: "chat_deletion_count" }))?.value || "0",

      // --------------------------------------------------------
      // Embedder Provider Selection Settings & Configs
      // --------------------------------------------------------
      EmbeddingEngine: process.env.EMBEDDING_ENGINE,
      HasExistingEmbeddings: await this.hasEmbeddings(), // check if they have any currently embedded documents active in workspaces.
      HasCachedEmbeddings: hasVectorCachedFiles(), // check if they any currently cached embedded docs.
      EmbeddingBasePath: process.env.EMBEDDING_BASE_PATH,
      EmbeddingModelPref: process.env.EMBEDDING_MODEL_PREF,
      EmbeddingModelMaxChunkLength:
        process.env.EMBEDDING_MODEL_MAX_CHUNK_LENGTH,
      GenericOpenAiEmbeddingApiKey:
        !!process.env.GENERIC_OPEN_AI_EMBEDDING_API_KEY,
      GeminiEmbeddingApiKey: !!process.env.GEMINI_EMBEDDING_API_KEY,
      ContextualEmbedding: process.env.CONTEXTUAL_EMBEDDING,
      ContextualSystemPrompt: process.env.CONTEXTUAL_SYSTEM_PROMPT,
      ContextualUserPrompt: process.env.CONTEXTUAL_USER_PROMPT,
      // Jina specific settings
      JinaApiKey: !!process.env.JINA_API_KEY,
      JinaDimensions: process.env.JINA_DIMENSIONS,
      JinaTask: process.env.JINA_TASK,
      JinaLateChunking: process.env.JINA_LATE_CHUNKING || "false",
      JinaEmbeddingType: process.env.JINA_EMBEDDING_TYPE,

      // --------------------------------------------------------
      // VectorDB Provider Selection Settings & Configs
      // --------------------------------------------------------

      // text splitting
      text_splitter_method:
        (await this.get({ label: "text_splitter_method" }))?.value || "native",
      text_splitter_chunk_size:
        (await this.get({ label: "text_splitter_chunk_size" }))?.value || 1000,
      text_splitter_chunk_overlap:
        (await this.get({ label: "text_splitter_chunk_overlap" }))?.value || 20,
      text_splitter_jina_max_tokens:
        (await this.get({ label: "text_splitter_jina_max_tokens" }))?.value ||
        1000,

      VectorDB: process.env.VECTOR_DB,
      ...this.vectorDBPreferenceKeys(),

      // --------------------------------------------------------
      // LLM Provider Selection Settings & Configs
      // --------------------------------------------------------
      LLMProvider: process.env.LLM_PROVIDER,
      LLMProvider_DD: process.env.LLM_PROVIDER_DD,
      LLMProvider_VA: process.env.LLM_PROVIDER_VA,
      LLMProvider_CDB: process.env.LLM_PROVIDER_CDB,
      LLMProvider_CUAI: process.env.LLM_PROVIDER_CUAI,
      LLMProvider_CUAI2: process.env.LLM_PROVIDER_CUAI2,
      LLMProvider_CUAI3: process.env.LLM_PROVIDER_CUAI3,
      LLMProvider_CUAI4: process.env.LLM_PROVIDER_CUAI4,
      LLMProvider_CUAI5: process.env.LLM_PROVIDER_CUAI5,
      LLMProvider_CUAI6: process.env.LLM_PROVIDER_CUAI6,
      LLMProvider_PU: process.env.LLM_PROVIDER_PU,
      LLMProvider_TM: process.env.LLM_PROVIDER_TM,
      LLMProvider_DD_2: process.env.LLM_PROVIDER_DD_2,
      BinaryLLM_DD: process.env.BINARY_LLM_DD,
      BinaryLLMUserLevel_DD: process.env.BINARY_LLM_USER_LEVEL_DD,

      ...this.llmPreferenceKeys(),

      // --------------------------------------------------------
      // Whisper (Audio transcription) Selection Settings & Configs
      // - Currently the only 3rd party is OpenAI, so is OPEN_AI_KEY is set
      // - then it can be shared.
      // --------------------------------------------------------
      WhisperProvider: process.env.WHISPER_PROVIDER || "local",
      WhisperModelPref:
        process.env.WHISPER_MODEL_PREF || "Xenova/whisper-small",

      // --------------------------------------------------------
      // TTS/STT  Selection Settings & Configs
      // - Currently the only 3rd party is OpenAI or the native browser-built in
      // --------------------------------------------------------
      TextToSpeechProvider: process.env.TTS_PROVIDER || "native",
      TTSOpenAIKey: !!process.env.TTS_OPEN_AI_KEY,
      TTSOpenAIVoiceModel: process.env.TTS_OPEN_AI_VOICE_MODEL,
      // Eleven Labs TTS
      TTSElevenLabsKey: !!process.env.TTS_ELEVEN_LABS_KEY,
      TTSElevenLabsVoiceModel: process.env.TTS_ELEVEN_LABS_VOICE_MODEL,
      // Piper TTS
      TTSPiperTTSVoiceModel:
        process.env.TTS_PIPER_VOICE_MODEL ?? "en_US-hfc_female-medium",

      // --------------------------------------------------------
      // Agent Settings & Configs
      // --------------------------------------------------------
      AgentGoogleSearchEngineId: process.env.AGENT_GSE_CTX || null,
      AgentGoogleSearchEngineKey: !!process.env.AGENT_GSE_KEY || null,
      AgentSearchApiKey: !!process.env.AGENT_SEARCHAPI_API_KEY || null,
      AgentSearchApiEngine: process.env.AGENT_SEARCHAPI_ENGINE || "google",
      AgentSerperApiKey: !!process.env.AGENT_SERPER_DEV_KEY || null,
      AgentBingSearchApiKey: !!process.env.AGENT_BING_SEARCH_API_KEY || null,
      AgentSerplyApiKey: !!process.env.AGENT_SERPLY_API_KEY || null,
      AgentSearXNGApiUrl: process.env.AGENT_SEARXNG_API_URL || null,
      AgentTavilyApiKey: !!process.env.AGENT_TAVILY_API_KEY || null,

      // SystemSettings
      language:
        (await SystemSettings.get({ label: "language" }))?.value || null,
      palette: (await SystemSettings.get({ label: "palette" }))?.value || null,

      // Custom User AI Settings
      custom_model_reference: await this.getValueOrFallback(
        { label: "custom_model_reference" },
        ""
      ),
      custom_model_reference_2: await this.getValueOrFallback(
        { label: "custom_model_reference_2" },
        ""
      ),
      custom_model_reference_3: await this.getValueOrFallback(
        { label: "custom_model_reference_3" },
        ""
      ),
      custom_model_reference_4: await this.getValueOrFallback(
        { label: "custom_model_reference_4" },
        ""
      ),
      custom_model_reference_5: await this.getValueOrFallback(
        { label: "custom_model_reference_5" },
        ""
      ),
      custom_model_reference_6: await this.getValueOrFallback(
        { label: "custom_model_reference_6" },
        ""
      ),
      dynamic_context_window_percentage: await this.getValueOrFallback(
        { label: "dynamic_context_window_percentage" },
        75
      ),
      custom_dynamic_context_window_percentage: await this.getValueOrFallback(
        { label: "custom_dynamic_context_window_percentage" },
        75
      ),
      custom_dynamic_context_window_percentage_2: await this.getValueOrFallback(
        { label: "custom_dynamic_context_window_percentage_2" },
        75
      ),
      custom_dynamic_context_window_percentage_3: await this.getValueOrFallback(
        { label: "custom_dynamic_context_window_percentage_3" },
        75
      ),
      custom_dynamic_context_window_percentage_4: await this.getValueOrFallback(
        { label: "custom_dynamic_context_window_percentage_4" },
        75
      ),
      custom_dynamic_context_window_percentage_5: await this.getValueOrFallback(
        { label: "custom_dynamic_context_window_percentage_5" },
        75
      ),
      custom_dynamic_context_window_percentage_6: await this.getValueOrFallback(
        { label: "custom_dynamic_context_window_percentage_6" },
        75
      ),
      CustomUserAiStatus_1: status1,
      CustomUserAiStatus_2: status2,
      CustomUserAiStatus_3: status3,
      CustomUserAiStatus_4: status4,
      CustomUserAiStatus_5: status5,
      CustomUserAiStatus_6: status6,
      request_legal_assistance_enabled: await this.getValueOrFallback(
        { label: "request_legal_assistance_enabled" },
        "false"
      ),
      request_legal_assistance_law_firm_name: await this.getValueOrFallback(
        { label: "request_legal_assistance_law_firm_name" },
        ""
      ),
      request_legal_assistance_email: await this.getValueOrFallback(
        { label: "request_legal_assistance_email" },
        ""
      ),
      disableValidationPrompt: safeJsonParse(
        (await this.get({ label: "disableValidationPrompt" }))?.value,
        false
      ),
    };
  },

  get: async function (clause = {}) {
    try {
      const setting = await prisma.system_settings.findFirst({ where: clause });
      return setting || null;
    } catch (error) {
      console.error(error.message);
      return null;
    }
  },

  getValueOrFallback: async function (clause = {}, fallback = null) {
    try {
      const setting = await this.get(clause);
      if (setting) {
        // Handle specific parsing logic here based on clause.label if needed
        if (clause.label === "custom_legal_templates") {
          const parsed = safeJsonParse(setting.value, null);
          // Ensure the result is an array, otherwise return the fallback
          return Array.isArray(parsed) ? parsed : fallback;
        } else if (clause.label === "vector_search_top_n") {
          return Number(setting.value) || fallback;
        }
        return setting.value || fallback;
      }
      return fallback;
    } catch (error) {
      console.error(error.message);
      return fallback;
    }
  },

  where: async function (clause = {}, limit) {
    try {
      const settings = await prisma.system_settings.findMany({
        where: clause,
        take: limit || undefined,
      });
      return settings;
    } catch (error) {
      console.error(error.message);
      return [];
    }
  },

  // Can take generic keys and will pre-filter invalid keys
  // from the set before sending to the explicit update function
  // that will then enforce validations as well.
  updateSettings: async function (updates = {}) {
    const validFields = Object.keys(updates).filter(
      (key) =>
        this.supportedFields.includes(key) || this.protectedFields.includes(key)
    );
    Object.entries(updates).forEach(([key]) => {
      if (validFields.includes(key)) return;
      delete updates[key];
    });
    return this._updateSettings(updates);
  },

  // Explicit update of settings + key validations.
  // Only use this method when directly setting a key value
  // that takes no user input for the keys being modified.
  _updateSettings: async function (updates = {}) {
    try {
      console.log("_updateSettings input:", updates);
      const updatePromises = [];
      const keys = Object.keys(updates);
      console.log("_updateSettings keys:", keys);
      for (const key of keys) {
        let validatedValue = updates[key];
        console.log(`_updateSettings key: ${key}, value:`, validatedValue);
        if (Object.prototype.hasOwnProperty.call(this.validations, key)) {
          const validationFn = this.validations[key];
          console.log(`Applying validation for ${key}`);
          validatedValue = await validationFn(updates[key]);
          console.log(`Validated ${key}:`, validatedValue);
          if (validatedValue === null) continue;
          updates[key] = validatedValue;
        }
        updatePromises.push(
          prisma.system_settings.upsert({
            where: { label: key },
            update: {
              value: validatedValue === null ? null : String(validatedValue),
            },
            create: {
              label: key,
              value: validatedValue === null ? null : String(validatedValue),
            },
          })
        );
      }
      console.log("_updateSettings promises:", updatePromises.length);
      await Promise.all(updatePromises);
      return { success: true, error: null };
    } catch (error) {
      console.error("FAILED TO UPDATE SYSTEM SETTINGS", error.message);
      return { success: false, error: error.message };
    }
  },

  isMultiUserMode: async function () {
    return true;
  },

  isPublicUserMode: async function () {
    try {
      const setting = await this.get({ label: "public_user_mode" });
      return setting?.value === "true";
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  isDocumentDrafting: async function () {
    try {
      const setting = await this.get({ label: "document_drafting" });
      return setting?.value === "true";
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  isDocumentDraftingLinking: async function () {
    try {
      const setting = await this.get({ label: "document_drafting_linking" });
      // Default to enabled if not explicitly set
      if (setting?.value == null) return true;
      return String(setting.value) === "true";
    } catch (error) {
      console.error(error.message);
      // On error, default to enabling linking
      return true;
    }
  },

  getDocumentDraftingPrompt: async function () {
    try {
      const setting = await this.get({ label: "document_drafting_prompt" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getLoginUi: async function () {
    try {
      const setting = await this.get({ label: "login_ui" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getDocumentDraftingLegalIssuesPrompt: async function () {
    try {
      const setting = await this.get({
        label: "document_drafting_legal_issue_prompt",
      });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getDocumentDraftingMemoPrompt: async function () {
    try {
      const setting = await this.get({
        label: "document_drafting_memo_prompt",
      });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getDocumentDraftingCombinePrompt: async function () {
    try {
      const setting = await this.get({
        label: "document_drafting_combine_prompt",
      });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getDefaultPrompt: async function () {
    try {
      const setting = await this.get({ label: "default_prompt" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getCanvasSystemPrompt: async function () {
    try {
      const setting = await this.get({ label: "canvas_system_prompt" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getCanvasUploadSystemPrompt: async function () {
    try {
      const setting = await this.get({ label: "canvas_upload_system_prompt" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getManualWorkEstimatorPrompt: async function () {
    try {
      const setting = await this.get({ label: "manual_work_estimator_prompt" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  getDefaultValidationPrompt: async function () {
    try {
      const setting = await this.get({ label: "validation_prompt" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  DEFAULT_PROMPT_TEMPLATE: `Your task is to refine a proposed prompt from a user who is a legal professional.
The prompt is intended for use in a legal task, but the user is not an expert in crafting optimal prompts for AI handling.
The prompt will also be used to search a vector database for relevant sources using semantic search.
Improve the prompt for clarity, detail, and specificity.
Ensure that the prompt is designed to generate results that are engaging, comprehensive, and specified according to professional standards in the legal domain.
Generate the response in the same language provided in original prompt.
Do not respond to the prompt but only provide a suggestion for an improved prompt.
Include no introductory text, just respond with the replacement prompt suggestion.
This is the prompt to be refined: <ORIGINALPROMPT> {{prompt}} </ORIGINALPROMPT>`,

  getPromptUpgradeTemplate: async function () {
    try {
      const setting = await this.get({ label: "prompt_upgrade_template" });
      return setting?.value || this.DEFAULT_PROMPT_TEMPLATE;
    } catch (error) {
      console.error(error.message);
      return this.DEFAULT_PROMPT_TEMPLATE;
    }
  },

  getPdrSettings: async function () {
    try {
      const [adjacentVector, keepPdrVectors, globalPdrOverride] =
        await Promise.all([
          this.get({ label: "adjacent_vector_limit" }),
          this.get({ label: "keep_pdr_vectors" }),
          this.get({ label: "global_pdr_override" }),
        ]);

      return {
        adjacentVector: adjacentVector?.value || null,
        keepPdrVectors: keepPdrVectors?.value === "false" ? false : true,
        globalPdrOverride: globalPdrOverride?.value === "false" ? false : true,
      };
    } catch (error) {
      console.error(error.message);
      return {
        adjacentVector: null,
        keepPdrVectors: true,
        globalPdrOverride: true,
      };
    }
  },

  getDynamicContextSettings: async function (suffix = "") {
    try {
      const defaultValue = 70;

      // If a suffix is provided, first try to fetch the specific setting for that model
      if (suffix) {
        // For suffix "_2", "_3", etc. we look for "custom_dynamic_context_window_percentage_2" etc.
        // For suffix "_CUAI" we look for "custom_dynamic_context_window_percentage" (no suffix in the key)
        let customKeySuffix;
        if (suffix === "_CUAI") {
          customKeySuffix = "";
        } else if (suffix.startsWith("_CUAI")) {
          // For _CUAI2, _CUAI3, etc., convert to _2, _3, etc.
          const numericPart = suffix.replace("_CUAI", "");
          customKeySuffix = `_${numericPart}`;
        } else {
          // For other formats like _2, _3, keep as is
          customKeySuffix = suffix;
        }

        const customSetting = await this.get({
          label: `custom_dynamic_context_window_percentage${customKeySuffix}`,
        });

        console.log(
          `[Dynamic Context Window] Looking up setting with label: custom_dynamic_context_window_percentage${customKeySuffix} for suffix ${suffix}`
        );

        if (
          customSetting &&
          customSetting.value !== undefined &&
          customSetting.value !== null
        ) {
          const customNumValue = Number(customSetting.value);
          if (!isNaN(customNumValue)) {
            console.log(
              `[Dynamic Context Window] Using custom value for suffix ${suffix}: ${customNumValue}%`
            );
            return customNumValue;
          }
        }

        // If specific setting not found or invalid, try the generic custom setting
        if (suffix !== "_CUAI") {
          const genericCustomSetting = await this.get({
            label: "custom_dynamic_context_window_percentage",
          });

          if (
            genericCustomSetting &&
            genericCustomSetting.value !== undefined &&
            genericCustomSetting.value !== null
          ) {
            const genericCustomNumValue = Number(genericCustomSetting.value);
            if (!isNaN(genericCustomNumValue)) {
              console.log(
                `[Dynamic Context Window] Using generic custom value for suffix ${suffix}: ${genericCustomNumValue}%`
              );
              return genericCustomNumValue;
            }
          }
        }
      }

      // Fall back to the default dynamic context window percentage
      const setting = await this.get({
        label: "dynamic_context_window_percentage",
      });

      if (!setting || setting.value === undefined || setting.value === null) {
        console.log(
          `[Dynamic Context Window] No value found in database, using DEFAULT value: ${defaultValue}%`
        );
        return defaultValue;
      }

      const numValue = Number(setting.value);
      if (isNaN(numValue)) {
        console.log(
          `[Dynamic Context Window] Invalid value in database: "${setting.value}", using DEFAULT value: ${defaultValue}%`
        );
        return defaultValue;
      }

      console.log(
        `[Dynamic Context Window] Using configured value from database: ${numValue}%`
      );
      return numValue;
    } catch (error) {
      console.error("Error fetching dynamic context settings:", error.message);
      console.log(
        `[Dynamic Context Window] Error occurred, using DEFAULT value: 70%`
      );
      return 70;
    }
  },

  getDDSettings: async function () {
    try {
      const [
        vectorEnabled,
        memoEnabled,
        baseEnabled,
        linkedWorkspaceImpact,
        vectorTokenLimit,
        memoTokenLimit,
        baseTokenLimit,
      ] = await Promise.all([
        this.get({ label: "dd_vector_enabled" }),
        this.get({ label: "dd_memo_enabled" }),
        this.get({ label: "dd_base_enabled" }),
        this.get({ label: "dd_linked_workspace_impact" }),
        this.get({ label: "dd_vector_token_limit" }),
        this.get({ label: "dd_memo_token_limit" }),
        this.get({ label: "dd_base_token_limit" }),
      ]);

      const parseBool = (setting, defaultValue = false) => {
        if (!setting || setting.value === undefined || setting.value === null) {
          return defaultValue;
        }
        return String(setting.value) === "true";
      };

      const parseNumber = (setting, defaultValue) => {
        const num = Number(setting?.value);
        return isNaN(num) ? defaultValue : num;
      };

      return {
        ddVectorEnabled: parseBool(vectorEnabled, false),
        ddMemoEnabled: parseBool(memoEnabled, true),
        ddBaseEnabled: parseBool(baseEnabled, true),
        ddLinkedWorkspaceImpact: parseBool(linkedWorkspaceImpact, true),
        ddVectorTokenLimit: parseNumber(vectorTokenLimit, 5000),
        ddMemoTokenLimit: parseNumber(memoTokenLimit, 4000),
        ddBaseTokenLimit: parseNumber(baseTokenLimit, 2000),
      };
    } catch (error) {
      console.error(error.message);
      return {
        ddVectorEnabled: false,
        ddMemoEnabled: true,
        ddBaseEnabled: true,
        ddLinkedWorkspaceImpact: true,
        ddVectorTokenLimit: 5000,
        ddMemoTokenLimit: 4000,
        ddBaseTokenLimit: 2000,
      };
    }
  },

  isQura: async function () {
    try {
      const setting = await this.get({ label: "qura" });
      return setting?.value === "true";
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  isPerformLegalTask: async function () {
    try {
      const performLegalTasks = await this.get({ label: "perform_legal_task" });
      const allowUserAccess = await this.get({ label: "allow_user_access" });

      return {
        enabled: performLegalTasks?.value !== "false",
        allowUserAccess: allowUserAccess?.value === "true",
      };
    } catch (error) {
      console.error("Error fetching system settings:", error.message);
      return { enabled: false, allowUserAccess: false };
    }
  },

  isFeedbackEnabled: async function () {
    try {
      const feedbackEnabled = await this.get({ label: "feedback_enabled" });
      return {
        enabled: feedbackEnabled?.value === "true",
      };
    } catch (error) {
      console.error("Error fetching feedback settings:", error.message);
      return { enabled: false };
    }
  },

  getCopyOption: async function () {
    try {
      const setting = await this.get({ label: "copyOption" });
      return setting?.value;
    } catch (error) {
      console.error("Failed to fetch copy option:", error.message);
      return null;
    }
  },

  getSystemWebsiteLinkAndText: async function () {
    try {
      const websiteSetting = await this.get({
        label: "websiteLink",
      });
      const displayTextSetting = await this.get({
        label: "displayText",
      });

      return {
        websiteLink: websiteSetting?.value || null,
        displayText: displayTextSetting?.value || "Visit the website",
      };
    } catch (error) {
      console.error(
        "Error fetching system website link and display text:",
        error.message
      );
      return false; // Return false if an error occurs
    }
  },

  getSystemTabNames: async function () {
    try {
      const tabName1Setting = await this.get({
        label: "tabName1",
      });
      const tabName2Setting = await this.get({
        label: "tabName2",
      });
      const tabName3Setting = await this.get({
        label: "tabName3",
      });

      return {
        tabName1: tabName1Setting?.value || null,
        tabName2: tabName2Setting?.value || null,
        tabName3: tabName3Setting?.value || null,
      };
    } catch (error) {
      console.error("Error fetching system tab names:", error.message);
      return false; // Return false if an error occurs
    }
  },

  getDeepSearchSettings: async function () {
    try {
      const provider = await this.get({ label: "deep_search_provider" });
      const modelId = await this.get({ label: "deep_search_model_id" });
      const apiKey = await this.get({ label: "deep_search_api_key" });
      const enabled = await this.get({ label: "deep_search_enabled" });
      const contextPercentage = await this.get({
        label: "deep_search_context_percentage",
      });

      return {
        provider: provider?.value || "duckduckgo",
        modelId: modelId?.value || "gemini-2.5-pro-exp-03-25",
        apiKey: apiKey?.value || "",
        enabled: enabled?.value === "true",
        contextPercentage: Number(contextPercentage?.value || 10),
      };
    } catch (error) {
      console.error("Error fetching DeepSearch settings:", error.message);
      return {
        provider: "duckduckgo",
        modelId: "gemini-2.5-pro-exp-03-25",
        apiKey: "",
        enabled: false,
        contextPercentage: 10,
      };
    }
  },

  updateDeepSearchSettings: async function (updates = {}) {
    try {
      const settingsToUpdate = [
        { label: "deep_search_provider", value: updates.provider },
        { label: "deep_search_model_id", value: updates.modelId },
        { label: "deep_search_api_key", value: updates.apiKey }, // Note: API key might need encryption before saving
        {
          label: "deep_search_enabled",
          value: String(updates.enabled ?? false),
        }, // Ensure value is string 'true' or 'false'
        {
          label: "deep_search_context_percentage",
          value: String(updates.contextPercentage ?? 10),
        }, // Ensure value is string
      ];

      // Filter out settings where the value wasn't provided in the updates
      const validUpdates = settingsToUpdate.filter((setting) =>
        updates.hasOwnProperty(this.mapLabelToInputKey(setting.label))
      );

      if (validUpdates.length === 0) {
        return { success: true }; // No valid updates to perform
      }

      // Use Prisma transaction to update all settings at once
      await prisma.$transaction(
        validUpdates.map((setting) => {
          // Apply validation if defined for the label
          const validatedRaw = this.validations[setting.label]
            ? this.validations[setting.label](setting.value)
            : setting.value;
          // TODO: Add API key encryption here if required before saving
          // if (setting.label === 'deep_search_api_key' && validatedValue) {
          //   validatedValue = encrypt(validatedValue);
          // }

          // Prisma expects a string or null for value
          const validatedValue =
            validatedRaw !== null ? String(validatedRaw) : null;
          return prisma.system_settings.upsert({
            where: { label: setting.label },
            update: { value: validatedValue },
            create: { label: setting.label, value: validatedValue },
          });
        })
      );

      return { success: true };
    } catch (error) {
      console.error("Error in updateDeepSearchSettings:", error);
      return { success: false, error: error.message };
    }
  },

  // Helper to map DB label back to the key used in the frontend updates object
  mapLabelToInputKey: function (label) {
    const map = {
      deep_search_provider: "provider",
      deep_search_model_id: "modelId",
      deep_search_api_key: "apiKey",
      deep_search_enabled: "enabled",
      deep_search_context_percentage: "contextPercentage",
    };
    return map[label] || null;
  },

  isCitation: async function () {
    try {
      const setting = await this.get({ label: "citation" });
      return setting?.value === "true";
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  isInvoiceEnabled: async function () {
    try {
      const setting = await this.get({ label: "invoice" });
      return setting?.value === "true";
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },
  isForcedInvoiceLoggingEnabled: async function () {
    try {
      const setting = await this.get({ label: "forced_invoice_logging" });
      return setting?.value === "true";
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  isRexorLinkageEnabled: async function () {
    try {
      const setting = await this.get({ label: "rexor_linkage" });
      return setting?.value === "true";
    } catch (error) {
      console.error("Error fetching rexor linkage setting:", error.message);
      return false;
    }
  },

  getRexorApiSettings: async function () {
    const defaults = {
      apiBaseUrl: "https://api.rexor.se/v231/Api",
      authUrl: "https://auth.rexor.se/v231/Token",
      clientIdDev: "testfoyen",
      clientIdProd: "foyen",
      apiHost: "api.rexor.se",
    };

    try {
      const [baseUrl, authUrl, clientIdDev, clientIdProd, apiHost] =
        await Promise.all([
          this.get({ label: "rexor_api_base_url" }),
          this.get({ label: "rexor_auth_url" }),
          this.get({ label: "rexor_client_id_dev" }),
          this.get({ label: "rexor_client_id_prod" }),
          this.get({ label: "rexor_api_host" }),
        ]);

      return {
        apiBaseUrl: baseUrl?.value || defaults.apiBaseUrl,
        authUrl: authUrl?.value || defaults.authUrl,
        clientIdDev: clientIdDev?.value || defaults.clientIdDev,
        clientIdProd: clientIdProd?.value || defaults.clientIdProd,
        apiHost: apiHost?.value || defaults.apiHost,
      };
    } catch (error) {
      console.error("Error fetching Rexor API settings:", error);
      return defaults;
    }
  },

  currentLogoLight: async function () {
    try {
      const setting = await this.get({ label: "logo_light" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return null;
    }
  },

  currentLogoDark: async function () {
    try {
      const setting = await this.get({ label: "logo_dark" });
      return setting?.value || null;
    } catch (error) {
      console.error(error.message);
      return null;
    }
  },

  hasEmbeddings: async function () {
    try {
      const { Document } = require("./documents");
      const count = await Document.count({}, 1);
      return count > 0;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  },

  vectorDBPreferenceKeys: function () {
    return {
      // Pinecone DB Keys
      PineConeKey: !!process.env.PINECONE_API_KEY,
      PineConeIndex: process.env.PINECONE_INDEX,

      // Chroma DB Keys
      ChromaEndpoint: process.env.CHROMA_ENDPOINT,
      ChromaApiHeader: process.env.CHROMA_API_HEADER,
      ChromaApiKey: !!process.env.CHROMA_API_KEY,

      // Weaviate DB Keys
      WeaviateEndpoint: process.env.WEAVIATE_ENDPOINT,
      WeaviateApiKey: process.env.WEAVIATE_API_KEY,

      // QDrant DB Keys
      QdrantEndpoint: process.env.QDRANT_ENDPOINT,
      QdrantApiKey: process.env.QDRANT_API_KEY,

      // Milvus DB Keys
      MilvusAddress: process.env.MILVUS_ADDRESS,
      MilvusUsername: process.env.MILVUS_USERNAME,
      MilvusPassword: !!process.env.MILVUS_PASSWORD,

      // Zilliz DB Keys
      ZillizEndpoint: process.env.ZILLIZ_ENDPOINT,
      ZillizApiToken: process.env.ZILLIZ_API_TOKEN,

      // AstraDB Keys
      AstraDBApplicationToken: process?.env?.ASTRA_DB_APPLICATION_TOKEN,
      AstraDBEndpoint: process?.env?.ASTRA_DB_ENDPOINT,
    };
  },

  llmPreferenceKeys: function () {
    const PROVIDER_SUFFIXES = {
      DEFAULT: "",
      DOCUMENT_DRAFTING: "_DD",
      DOCUMENT_DRAFTING_2: "_DD_2",
      VALIDATE_ANSWER: "_VA",
      CONTEXTUAL_DB: "_CDB",
      PROMPT_UPGRADE: "_PU",
      TEMPLATE_MODAL: "_TM",
      CUSTOM_USER_AI: "_CUAI",
      CUSTOM_USER_AI2: "_CUAI2",
      CUSTOM_USER_AI3: "_CUAI3",
      CUSTOM_USER_AI4: "_CUAI4",
      CUSTOM_USER_AI5: "_CUAI5",
      CUSTOM_USER_AI6: "_CUAI6",
    };

    const generateSuffixedSettings = (
      baseKey,
      baseEnvKey,
      defaultValue = null,
      transformFunc = (val) => val
    ) => {
      const settings = {};

      Object.values(PROVIDER_SUFFIXES).forEach((suffix) => {
        const key = baseKey + suffix;
        const envKey = baseEnvKey + suffix;

        if (defaultValue !== null) {
          settings[key] = transformFunc(process.env[envKey] || defaultValue);
        } else {
          settings[key] = transformFunc(process.env[envKey]);
        }
      });

      return settings;
    };

    return {
      // OpenAI Keys
      ...generateSuffixedSettings(
        "OpenAiKey",
        "OPEN_AI_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "OpenAiModelPref",
        "OPEN_MODEL_PREF",
        "chatgpt-4o-latest"
      ),

      // Azure + OpenAI Keys
      ...generateSuffixedSettings(
        "AzureOpenAiEndpoint",
        "AZURE_OPENAI_ENDPOINT"
      ),
      ...generateSuffixedSettings(
        "AzureOpenAiKey",
        "AZURE_OPENAI_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings("AzureOpenAiModelPref", "OPEN_MODEL_PREF"),
      ...generateSuffixedSettings(
        "AzureOpenAiEmbeddingModelPref",
        "EMBEDDING_MODEL_PREF"
      ),
      ...generateSuffixedSettings(
        "AzureOpenAiTokenLimit",
        "AZURE_OPENAI_TOKEN_LIMIT",
        4096,
        (val) => val || 4096
      ),

      // Anthropic Keys
      ...generateSuffixedSettings(
        "AnthropicApiKey",
        "ANTHROPIC_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "AnthropicModelPref",
        "ANTHROPIC_MODEL_PREF",
        "claude-2"
      ),

      // Gemini Keys
      ...generateSuffixedSettings(
        "GeminiLLMApiKey",
        "GEMINI_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "GeminiLLMModelPref",
        "GEMINI_LLM_MODEL_PREF",
        "gemini-pro"
      ),
      ...generateSuffixedSettings(
        "GeminiSafetySetting",
        "GEMINI_SAFETY_SETTING",
        "BLOCK_MEDIUM_AND_ABOVE"
      ),

      // LMStudio Keys
      ...generateSuffixedSettings("LMStudioBasePath", "LMSTUDIO_BASE_PATH"),
      ...generateSuffixedSettings("LMStudioModelPref", "LMSTUDIO_MODEL_PREF"),
      ...generateSuffixedSettings(
        "LMStudioTokenLimit",
        "LMSTUDIO_MODEL_TOKEN_LIMIT"
      ),

      // LocalAI Keys
      ...generateSuffixedSettings(
        "LocalAiApiKey",
        "LOCAL_AI_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings("LocalAiBasePath", "LOCAL_AI_BASE_PATH"),
      ...generateSuffixedSettings("LocalAiModelPref", "LOCAL_AI_MODEL_PREF"),
      ...generateSuffixedSettings(
        "LocalAiTokenLimit",
        "LOCAL_AI_MODEL_TOKEN_LIMIT"
      ),

      // Ollama LLM Keys
      ...generateSuffixedSettings("OllamaLLMBasePath", "OLLAMA_BASE_PATH"),
      ...generateSuffixedSettings("OllamaLLMModelPref", "OLLAMA_MODEL_PREF"),
      ...generateSuffixedSettings(
        "OllamaLLMTokenLimit",
        "OLLAMA_MODEL_TOKEN_LIMIT"
      ),
      ...generateSuffixedSettings(
        "OllamaLLMKeepAliveSeconds",
        "OLLAMA_KEEP_ALIVE_TIMEOUT",
        300,
        (val) => val ?? 300
      ),
      ...generateSuffixedSettings(
        "OllamaLLMPerformanceMode",
        "OLLAMA_PERFORMANCE_MODE",
        "base",
        (val) => val ?? "base"
      ),

      // TogetherAI Keys
      ...generateSuffixedSettings(
        "TogetherAiApiKey",
        "TOGETHER_AI_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "TogetherAiModelPref",
        "TOGETHER_AI_MODEL_PREF"
      ),

      // Fireworks AI API Keys
      ...generateSuffixedSettings(
        "FireworksAiLLMApiKey",
        "FIREWORKS_AI_LLM_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "FireworksAiLLMModelPref",
        "FIREWORKS_AI_LLM_MODEL_PREF"
      ),

      // Perplexity AI Keys
      ...generateSuffixedSettings(
        "PerplexityApiKey",
        "PERPLEXITY_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "PerplexityModelPref",
        "PERPLEXITY_MODEL_PREF"
      ),

      // OpenRouter Keys
      ...generateSuffixedSettings(
        "OpenRouterApiKey",
        "OPENROUTER_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "OpenRouterModelPref",
        "OPENROUTER_MODEL_PREF"
      ),

      // Mistral AI (API) Keys
      ...generateSuffixedSettings(
        "MistralApiKey",
        "MISTRAL_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings("MistralModelPref", "MISTRAL_MODEL_PREF"),

      // Groq AI API Keys
      ...generateSuffixedSettings(
        "GroqApiKey",
        "GROQ_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings("GroqModelPref", "GROQ_MODEL_PREF"),

      // Native LLM Keys
      ...generateSuffixedSettings(
        "NativeLLMModelPref",
        "NATIVE_LLM_MODEL_PREF"
      ),
      ...generateSuffixedSettings(
        "NativeLLMTokenLimit",
        "NATIVE_LLM_MODEL_TOKEN_LIMIT"
      ),

      // HuggingFace Dedicated Inference Endpoint
      ...generateSuffixedSettings(
        "HuggingFaceLLMEndpoint",
        "HUGGING_FACE_LLM_ENDPOINT"
      ),
      ...generateSuffixedSettings(
        "HuggingFaceLLMAccessToken",
        "HUGGING_FACE_LLM_API_KEY",
        null,
        (val) => !!val
      ),
      ...generateSuffixedSettings(
        "HuggingFaceLLMTokenLimit",
        "HUGGING_FACE_LLM_TOKEN_LIMIT"
      ),

      // KoboldCPP Model Pref
      ...generateSuffixedSettings(
        "KoboldCPPModelPref",
        "KOBOLD_CPP_MODEL_PREF"
      ),

      // KoboldCPP Base Path
      ...generateSuffixedSettings("KoboldCPPBasePath", "KOBOLD_CPP_BASE_PATH"),

      // KoboldCPP Token Limit
      ...generateSuffixedSettings(
        "KoboldCPPTokenLimit",
        "KOBOLD_CPP_MODEL_TOKEN_LIMIT"
      ),

      // Text Generation Web UI BasePath
      ...generateSuffixedSettings(
        "TextGenWebUIBasePath",
        "TEXT_GEN_WEB_UI_BASE_PATH"
      ),

      // Text Generation Web UI TokenLimit
      ...generateSuffixedSettings(
        "TextGenWebUITokenLimit",
        "TEXT_GEN_WEB_UI_MODEL_TOKEN_LIMIT"
      ),

      // Text Generation Web UI API Key
      ...generateSuffixedSettings(
        "TextGenWebUIAPIKey",
        "TEXT_GEN_WEB_UI_API_KEY",
        null,
        (val) => !!val
      ),

      // LiteLLM Model Pref
      ...generateSuffixedSettings("LiteLLMModelPref", "LITE_LLM_MODEL_PREF"),

      // LiteLLM Token Limit
      ...generateSuffixedSettings(
        "LiteLLMTokenLimit",
        "LITE_LLM_MODEL_TOKEN_LIMIT"
      ),

      // LiteLLM Base Path
      ...generateSuffixedSettings("LiteLLMBasePath", "LITE_LLM_BASE_PATH"),

      // LiteLLM API Key
      ...generateSuffixedSettings(
        "LiteLLMApiKey",
        "LITE_LLM_API_KEY",
        null,
        (val) => !!val
      ),

      // Generic OpenAI Keys
      ...generateSuffixedSettings(
        "GenericOpenAiBasePath",
        "GENERIC_OPEN_AI_BASE_PATH"
      ),

      // Generic OpenAI Model Pref
      ...generateSuffixedSettings(
        "GenericOpenAiModelPref",
        "GENERIC_OPEN_AI_MODEL_PREF"
      ),

      // Generic OpenAI Token Limit
      ...generateSuffixedSettings(
        "GenericOpenAiTokenLimit",
        "GENERIC_OPEN_AI_MODEL_TOKEN_LIMIT"
      ),

      // Generic OpenAI API Key
      ...generateSuffixedSettings(
        "GenericOpenAiKey",
        "GENERIC_OPEN_AI_API_KEY",
        null,
        (val) => !!val
      ),

      // Generic OpenAI Max Tokens
      ...generateSuffixedSettings(
        "GenericOpenAiMaxTokens",
        "GENERIC_OPEN_AI_MAX_TOKENS"
      ),

      // AWS Bedrock Access Key ID
      ...generateSuffixedSettings(
        "AwsBedrockLLMAccessKeyId",
        "AWS_BEDROCK_LLM_ACCESS_KEY_ID",
        null,
        (val) => !!val
      ),

      // AWS Bedrock Access Key
      ...generateSuffixedSettings(
        "AwsBedrockLLMAccessKey",
        "AWS_BEDROCK_LLM_ACCESS_KEY",
        null,
        (val) => !!val
      ),

      // AWS Bedrock Region
      ...generateSuffixedSettings(
        "AwsBedrockLLMRegion",
        "AWS_BEDROCK_LLM_REGION"
      ),

      // AWS Bedrock Model Preference
      ...generateSuffixedSettings(
        "AwsBedrockLLMModel",
        "AWS_BEDROCK_LLM_MODEL_PREFERENCE"
      ),

      // AWS Bedrock Token Limit
      ...generateSuffixedSettings(
        "AwsBedrockLLMTokenLimit",
        "AWS_BEDROCK_LLM_MODEL_TOKEN_LIMIT"
      ),

      // Cohere API Keys
      ...generateSuffixedSettings(
        "CohereApiKey",
        "COHERE_API_KEY",
        null,
        (val) => !!val
      ),

      // Cohere Model Preference
      ...generateSuffixedSettings("CohereModelPref", "COHERE_MODEL_PREF"),

      // VoyageAi API Keys
      ...generateSuffixedSettings(
        "VoyageAiApiKey",
        "VOYAGEAI_API_KEY",
        null,
        (val) => !!val
      ),

      // DeepSeek API Keys
      ...generateSuffixedSettings(
        "DeepSeekApiKey",
        "DEEPSEEK_API_KEY",
        null,
        (val) => !!val
      ),

      // DeepSeek Model Preference
      ...generateSuffixedSettings("DeepSeekModelPref", "DEEPSEEK_MODEL_PREF"),

      // xAI LLM API Keys
      ...generateSuffixedSettings(
        "XAIApiKey",
        "XAI_LLM_API_KEY",
        null,
        (val) => !!val
      ),

      // xAI LLM Model Preferences
      ...generateSuffixedSettings("XAIModelPref", "XAI_LLM_MODEL_PREF"),

      // Jina API Keys
      ...generateSuffixedSettings(
        "JinaApiKey",
        "JINA_API_KEY",
        null,
        (val) => !!val
      ),
    };
  },

  // For special retrieval of a key setting that does not expose any credential information
  brief: {
    agent_sql_connections: async function () {
      const setting = await SystemSettings.get({
        label: "agent_sql_connections",
      });
      if (!setting) return [];
      return safeJsonParse(setting.value, []).map((dbConfig) => {
        const { connectionString, ...rest } = dbConfig;
        return rest;
      });
    },
  },
  getFeatureFlags: async function () {
    return {
      experimental_live_file_sync:
        (await SystemSettings.get({ label: "experimental_live_file_sync" }))
          ?.value === "enabled",
    };
  },

  isPromptOutputLogging: async function () {
    const value = await this.getValueOrFallback(
      { label: "prompt_output_logging" },
      true
    );
    return value === true || value === "true";
  },

  getMaxTokensPerUser: async function () {
    const setting = await this.getValueOrFallback(
      { label: "max_tokens_per_user" },
      1
    );
    return this.validations.max_tokens_per_user(setting);
  },

  getRequestLegalAssistanceSettings: async function () {
    try {
      const [enabledRaw, lawFirmNameRaw, emailRaw] = await Promise.all([
        this.getValueOrFallback(
          { label: "request_legal_assistance_enabled" },
          false
        ),
        this.getValueOrFallback(
          { label: "request_legal_assistance_law_firm_name" },
          null
        ),
        this.getValueOrFallback(
          { label: "request_legal_assistance_email" },
          ""
        ),
      ]);

      const enabled =
        this.validations.request_legal_assistance_enabled(enabledRaw);
      const lawFirmName =
        this.validations.request_legal_assistance_law_firm_name(lawFirmNameRaw);
      const email = this.validations.request_legal_assistance_email(emailRaw);

      return {
        enabled,
        lawFirmName,
        email,
      };
    } catch (error) {
      console.error(
        "Error fetching request legal assistance settings:",
        error.message
      );
      return {
        enabled: false,
        lawFirmName: null,
        email: null,
      };
    }
  },
};

function mergeConnections(existingConnections = [], updates = []) {
  let updatedConnections = [...existingConnections];
  const existingDbIds = existingConnections.map((conn) => conn.database_id);

  // First remove all 'action:remove' candidates from existing connections.
  const toRemove = updates
    .filter((conn) => conn.action === "remove")
    .map((conn) => conn.database_id);
  updatedConnections = updatedConnections.filter(
    (conn) => !toRemove.includes(conn.database_id)
  );

  // Next add all 'action:add' candidates into the updatedConnections; We DO NOT validate the connection strings.
  // but we do validate their database_id is unique.
  updates
    .filter((conn) => conn.action === "add")
    .forEach((update) => {
      if (!update.connectionString) return; // invalid connection string

      // Remap name to be unique to entire set.
      if (existingDbIds.includes(update.database_id)) {
        update.database_id = slugify(
          `${update.database_id}-${v4().slice(0, 4)}`
        );
      } else {
        update.database_id = slugify(update.database_id);
      }

      updatedConnections.push({
        engine: update.engine,
        database_id: update.database_id,
        connectionString: update.connectionString,
      });
    });

  return updatedConnections;
}

module.exports.SystemSettings = SystemSettings;
