jest.mock("../../utils/middleware/validatedRequest.js", () => ({
  validatedRequest: (req, res, next) => {
    next();
  },
}));

const request = require("supertest");
const app = require("../../index"); // Adjust path to your main Express app file
const fs = require("fs");
const path = require("path");
const LEGAL_DRAFTING_PROMPTS = require("../../utils/chats/prompts/legalDrafting");

// Define paths to mock documentation files for clarity, though fs will be mocked
const streamCDBActualPath = path.resolve(
  __dirname,
  "../../../server/docs/streamCDB.md"
);
const legalFlowsActualPath = path.resolve(
  __dirname,
  "../../../server/docs/legal_drafting_flows.md"
);

const streamCDBMockContent =
  "# Mock StreamCDB Overview\nThis is the base overview.";
const legalFlowsMockContent =
  "# Mock Legal Drafting Flows\nDetails about flows.";

describe("GET /system/cdb-documentation", () => {
  beforeEach(() => {
    // Spy on fs methods and mock their implementations
    jest.spyOn(fs, "existsSync").mockImplementation((filePath) => {
      // Default to true for expected paths, can be overridden in specific tests
      if (filePath === streamCDBActualPath) return true;
      if (filePath === legalFlowsActualPath) return true;
      return false;
    });

    jest.spyOn(fs, "readFileSync").mockImplementation((filePath, encoding) => {
      // Default to returning mock content for expected paths, can be overridden
      if (filePath === streamCDBActualPath) return streamCDBMockContent;
      if (filePath === legalFlowsActualPath) return legalFlowsMockContent;
      return "";
    });
  });

  afterEach(() => {
    jest.restoreAllMocks(); // Restore original fs functions
  });

  it("should return 200 OK and combined documentation with all parts", async () => {
    const response = await request(app).get("/api/system/cdb-documentation");
    // .set('Authorization', `Bearer TEST_TOKEN`); // Assuming validatedRequest is passed for these system endpoints

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.documentation).toBeDefined();

    const { documentation } = response.body;

    expect(documentation).toContain(streamCDBMockContent);
    expect(documentation).toContain("### Detailed Legal Drafting Flows");
    expect(documentation).toContain(legalFlowsMockContent);
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
    expect(documentation).toContain(
      "(these can be overridden by system settings):"
    );
    expect(documentation).toContain("### DEFAULT_DOCUMENT_SUMMARY");
    expect(documentation).toContain(
      LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT
    );
    expect(documentation).toContain("### DEFAULT_SECTION_LIST_FROM_MAIN");
    expect(documentation).toContain(
      LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT
    );
  });

  it("should handle missing legal_drafting_flows.md gracefully", async () => {
    fs.existsSync.mockImplementation(
      (filePath) => filePath === streamCDBActualPath
    );
    // readFileSync for legalFlowsActualPath will implicitly return '' due to default mock setup if not overridden,
    // or one could be more explicit:
    // fs.readFileSync.mockImplementation((filePath, encoding) => {
    //   if (filePath === streamCDBActualPath) return streamCDBMockContent;
    //   return '';
    // });

    const response = await request(app).get("/api/system/cdb-documentation");
    // .set('Authorization', `Bearer TEST_TOKEN`);

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    expect(documentation).toContain(streamCDBMockContent);
    expect(documentation).not.toContain("### Detailed Legal Drafting Flows");
    expect(documentation).not.toContain(legalFlowsMockContent);
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
  });

  it("should handle missing streamCDB.md by serving fallback base", async () => {
    fs.existsSync.mockImplementation(
      (filePath) => filePath === legalFlowsActualPath
    );
    // fs.readFileSync.mockImplementation((filePath, encoding) => {
    //   if (filePath === legalFlowsActualPath) return legalFlowsMockContent;
    //   return ''; // streamCDB.md will be considered not found by readFileSync if existsSync was false
    // });

    const response = await request(app).get("/api/system/cdb-documentation");
    // .set('Authorization', `Bearer TEST_TOKEN`);

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    expect(documentation).toContain("# Case Document Builder (CDB) Overview");
    expect(documentation).toContain(
      "This document provides an overview of the CDB functionality."
    );
    expect(documentation).toContain("### Detailed Legal Drafting Flows");
    expect(documentation).toContain(legalFlowsMockContent);
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
  });

  it("should handle both documentation files missing by serving fallback base and defaults", async () => {
    fs.existsSync.mockReturnValue(false); // Neither file exists
    // fs.readFileSync.mockReturnValue(''); // Will not be called if existsSync is false

    const response = await request(app).get("/api/system/cdb-documentation");
    // .set('Authorization', `Bearer TEST_TOKEN`);

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    expect(documentation).toContain("# Case Document Builder (CDB) Overview");
    expect(documentation).not.toContain("### Detailed Legal Drafting Flows");
    expect(documentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
  });

  it("should return 500 if an unexpected error occurs during file processing", async () => {
    // Ensure existsSync returns true for a path that readFileSync will then throw an error on
    fs.existsSync.mockImplementation(
      (filePath) => filePath === streamCDBActualPath
    );
    fs.readFileSync.mockImplementation((filePath, encoding) => {
      if (filePath === streamCDBActualPath) throw new Error("Disk read error");
      return "";
    });

    const response = await request(app).get("/api/system/cdb-documentation");
    // .set('Authorization', `Bearer TEST_TOKEN`);

    expect(response.statusCode).toBe(500);
    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe("Failed to fetch CDB documentation");
    expect(response.body.details).toBe("Disk read error");
  });

  it("should fetch and combine actual .md files with default prompts (integration test)", async () => {
    // Restore original fs functions for this specific test to hit the real file system
    jest.restoreAllMocks();

    // Read actual file contents for assertion and normalize line endings
    const rawStreamCDBContent = fs.readFileSync(streamCDBActualPath, "utf-8");
    const actualStreamCDBContent = rawStreamCDBContent
      .replace(/^\uFEFF/, "")
      .replace(/\r\n/g, "\n");

    const rawLegalFlowsContent = fs.readFileSync(legalFlowsActualPath, "utf-8");
    const actualLegalFlowsContent = rawLegalFlowsContent
      .replace(/^\uFEFF/, "")
      .replace(/\r\n/g, "\n");

    const response = await request(app).get("/api/system/cdb-documentation");

    expect(response.statusCode).toBe(200);
    expect(response.body.success).toBe(true);
    const { documentation } = response.body;
    const normalizedDocumentation = documentation.replace(/\r\n/g, "\n");

    // Check for actual content of streamCDB.md
    expect(normalizedDocumentation).toContain(actualStreamCDBContent);

    // Check for the specific header and actual content of legal_drafting_flows.md
    const legalFlowsHeader = "### Detailed Legal Drafting Flows\n";
    const headerIndex = normalizedDocumentation.indexOf(legalFlowsHeader);

    // Assertion 1: Header must be present
    if (headerIndex === -1) {
      console.log(
        "DEBUG: Legal flows header not found in normalizedDocumentation."
      );
      // Optionally log a snippet of normalizedDocumentation if header is missing
      // console.log("DEBUG: Snippet of normalizedDocumentation:", normalizedDocumentation.substring(0, 500));
    }
    expect(headerIndex).toBeGreaterThan(-1);

    const expectedContentStartIndex = headerIndex + legalFlowsHeader.length;
    const contentActuallyFollowingHeader = normalizedDocumentation.substring(
      expectedContentStartIndex,
      expectedContentStartIndex + actualLegalFlowsContent.length
    );

    // Assertion 2: The content immediately following the header should match actualLegalFlowsContent
    if (contentActuallyFollowingHeader !== actualLegalFlowsContent) {
      console.log(
        "DEBUG: Mismatch between content following header and actualLegalFlowsContent."
      );
      console.log(
        "DEBUG: Expected content (actualLegalFlowsContent snippet):\n",
        actualLegalFlowsContent.substring(0, 200)
      );
      console.log(
        "DEBUG: Content actually following header (snippet):\n",
        contentActuallyFollowingHeader.substring(0, 200)
      );
      // For more detailed diff, you could log both full strings if they aren't excessively long
      // or use a diffing library if this were outside Jest's default output.
    }
    expect(contentActuallyFollowingHeader.trim()).toBe(
      ("\n" + actualLegalFlowsContent).trim()
    );

    // Check for the presence of the default prompts section header
    expect(normalizedDocumentation).toContain(
      "### Default CDB Prompt Templates Used by Flows"
    );
    expect(normalizedDocumentation).toContain(
      "(these can be overridden by system settings):"
    );

    // Check for a sample default prompt to ensure that part is also included
    // Normalize the prompt from the module as well, in case it contains \r\n from its definition or BOM
    const normalizedDefaultSummaryPrompt =
      LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT.replace(
        /^\uFEFF/,
        ""
      ).replace(/\r\n/g, "\n");
    expect(normalizedDocumentation).toContain("### DEFAULT_DOCUMENT_SUMMARY");
    expect(normalizedDocumentation).toContain(normalizedDefaultSummaryPrompt);
  });
});
