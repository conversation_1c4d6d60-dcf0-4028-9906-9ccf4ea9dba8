#!/usr/bin/env node

import { PrismaClient } from "../server/node_modules/.prisma/client/index.js";
import bcrypt from "bcryptjs";
import { fileURLToPath } from "url";
import { dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const prisma = new PrismaClient();
const SALT_ROUNDS = 10;

async function resetDatabase() {
  try {
    // Reset the database
    console.log("🗑️  Resetting database...");
    await prisma.$executeRaw`DELETE FROM workspace_documents;`;
    await prisma.$executeRaw`DELETE FROM workspace_users;`;
    await prisma.$executeRaw`DELETE FROM workspaces;`;
    await prisma.$executeRaw`DELETE FROM users;`;
    await prisma.$executeRaw`DELETE FROM system_settings;`;
    console.log("✅ Database reset complete");

    // Create admin user
    console.log("👤 Creating admin user...");
    const hashedPassword = await bcrypt.hash("admin", SALT_ROUNDS);
    const admin = await prisma.users.create({
      data: {
        username: "admin",
        password: hashedPassword,
        role: "admin",
      },
    });
    console.log("✅ Admin user created");

    // Initialize system settings
    console.log("⚙️  Initializing system settings...");
    const settings = [
      { label: "multi_user_mode", value: "true" },
      { label: "public_user_mode", value: "false" },
      { label: "limit_user_messages", value: "false" },
      { label: "message_limit", value: "25" },
      { label: "logo_light", value: "ISTLogo.png" },
      { label: "logo_dark", value: "ISTLogo.png" },
    ];

    for (const setting of settings) {
      await prisma.system_settings.create({
        data: setting,
      });
    }
    console.log("✅ System settings initialized");

    // Create default workspace
    console.log("🏢 Creating default workspace...");
    const workspace = await prisma.workspaces.create({
      data: {
        name: "Default Workspace",
        slug: "default",
        user_id: admin.id,
        chatMode: "chat",
        chatType: "private",
      },
    });

    // Link admin to workspace
    await prisma.workspace_users.create({
      data: {
        user_id: admin.id,
        workspace_id: workspace.id,
      },
    });
    console.log("✅ Default workspace created");

    console.log("\n🎉 Database initialization complete!");
    console.log("\nDefault admin credentials:");
    console.log("Username: admin");
    console.log("Password: admin");

    console.log(
      "Paste into your browser console:\n  localStorage.removeItem('workspace-store');"
    );
    console.log(
      "A successful call returns undefined. Verify by running:\n  localStorage.getItem('workspace-store');"
    );
    console.log("Then refresh http://localhost:3000 to see the reset data.");
  } catch (error) {
    console.error("❌ Error during database reset:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the reset function
resetDatabase();
